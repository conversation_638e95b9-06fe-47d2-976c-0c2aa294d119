# CodeRabbit Issue: Duplicate Instructions Extraction

## Issue Summary
**Location**: `apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts`  
**Lines**: 84-130  
**Type**: Code quality/optimization  
**Priority**: Low-Medium  

## Description
Instructions are extracted twice in the `request-rush-processing.handler.ts` file:
1. Once in the `handle` method (line 84)
2. Again in the `buildMainMessages` method (line 130)

## Current Implementation
```typescript
// Line 84 in handle method
const instructions = this.extractInstructions(validatedIntent);

// Line 130 in buildMainMessages method  
const instructions = this.extractInstructions(validatedIntent);
```

## Analysis
- **Performance Impact**: Minimal (just property access with null coalescing)
- **Code Quality**: Creates unnecessary duplication
- **Pattern**: This duplication exists across multiple handlers (request-manual-processing, request-hold-shipment, request-rush-processing)
- **Root Cause**: Architectural pattern rather than accidental oversight

## Recommended Fix
Pass instructions as a parameter to avoid duplicate extraction:

```typescript
async handle(validatedIntent: ValidatedIntent, context: ShipmentContextWithServices): Promise<ResponseFragment[]> {
  const instructions = this.extractInstructions(validatedIntent);
  
  // ... validation logic ...
  
  const mainMessages = await this.buildMainMessages(validatedIntent, context, instructions);
  // ... rest of method
}

private async buildMainMessages(
  validatedIntent: ValidatedIntent,
  context: ShipmentContextWithServices,
  instructions: string[]  // Add as parameter
): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
  // Remove the duplicate extraction line
  // const instructions = this.extractInstructions(validatedIntent);
  
  // ... rest of method uses the passed instructions
}
```

## Affected Files
This pattern affects multiple handlers and should be fixed consistently:
- `request-rush-processing.handler.ts` (lines 84, 130)
- `request-manual-processing.handler.ts` (lines 32, 80) 
- `request-hold-shipment.handler.ts` (lines 35, 83)

## Status
- **Verified**: ✅ Issue confirmed
- **Impact**: Low (code quality improvement)
- **Fix Required**: Yes, but not urgent
- **Scope**: Multiple handlers need consistent fix

## Notes
This is a minor optimization that should be addressed during the next refactoring cycle rather than as an urgent fix. The performance impact is negligible, but fixing it would improve code maintainability and reduce duplication.