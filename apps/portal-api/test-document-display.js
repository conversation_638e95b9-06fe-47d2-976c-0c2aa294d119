#!/usr/bin/env node

const { NestFactory } = require("@nestjs/core");
const { AppModule } = require("./dist/app.module");
const { DocumentProcessorService } = require("./dist/core-agent/services/document-processor.service");

async function main() {
  const app = await NestFactory.createApplicationContext(AppModule);
  const documentProcessor = app.get(DocumentProcessorService);

  // Example 1: Documents with various statuses
  console.log("=== Document Processing Display Examples ===\n");

  // Test the formatDocumentTypeName method
  console.log("1. Document Type Name Formatting:");
  console.log("   COMMERCIAL_INVOICE ->", documentProcessor.formatDocumentTypeName("COMMERCIAL_INVOICE"));
  console.log("   BILL_OF_LADING ->", documentProcessor.formatDocumentTypeName("BILL_OF_LADING"));
  console.log("   PACKING_LIST ->", documentProcessor.formatDocumentTypeName("PACKING_LIST"));
  console.log("   undefined ->", documentProcessor.formatDocumentTypeName(undefined));
  console.log("   '' ->", documentProcessor.formatDocumentTypeName(""));

  // Test the formatStatusForDisplay method (private, so we'll simulate what it does)
  console.log("\n2. Status Display Formatting:");
  const formatStatusForDisplay = (status) => status.replace(/_/g, " ").toLowerCase();
  console.log("   EXTRACTION_COMPLETED ->", formatStatusForDisplay("EXTRACTION_COMPLETED"));
  console.log("   EXTRACTION_FAILED ->", formatStatusForDisplay("EXTRACTION_FAILED"));
  console.log("   VALIDATED ->", formatStatusForDisplay("VALIDATED"));
  console.log("   PROCESSING ->", formatStatusForDisplay("PROCESSING"));

  // Simulate what the template would render
  console.log("\n3. Template Rendering Example:");
  const mockDocuments = [
    {
      filename: "Commercial Invoice.pdf",
      contentType: "Commercial Invoice",
      status: "extraction completed",
      claroUrl: "https://portal.clarocustoms.com/document/123"
    },
    {
      filename: "Bill of Lading.pdf", 
      contentType: "Bill Of Lading",
      status: "validated",
      claroUrl: "https://portal.clarocustoms.com/document/124"
    },
    {
      filename: "Document", // fallback case
      contentType: "Document", 
      status: "processed", // fallback status
      claroUrl: "https://portal.clarocustoms.com/documents"
    }
  ];

  console.log("   Documents that would be displayed:");
  mockDocuments.forEach((doc, index) => {
    console.log(`   ${index + 1}. ${doc.filename}`);
    console.log(`      - Document Type: ${doc.contentType}`);
    console.log(`      - Status: ${doc.status}`);
    console.log(`      - Claro URL: ${doc.claroUrl}`);
    console.log("");
  });

  console.log("4. HTML Output Preview:");
  console.log("   We've received and processed the following document(s):");
  mockDocuments.forEach((doc, index) => {
    console.log(`   [${index + 1}] ${doc.filename}`);
    console.log(`       Document Type: ${doc.contentType}`);
    console.log(`       Status: ${doc.status}`);
    console.log(`       Claro URL: ${doc.claroUrl}`);
    console.log("");
  });

  await app.close();
}

if (require.main === module) {
  main().catch(console.error);
}