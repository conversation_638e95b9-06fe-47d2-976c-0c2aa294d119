#!/usr/bin/env node

/**
 * Direct Processor Testing Script
 *
 * This script allows direct testing of the handle-request-message.processor.ts
 * by feeding in different intents and shipment data to see what templates and
 * output they produce. This bypasses the full email pipeline and focuses on
 * testing the processor logic directly.
 *
 * Usage:
 *   node src/core-agent/testing/test-processor-direct.js [options]
 *
 * Options:
 *   --org=ID                   Organization ID (default: 3 - demo org)
 *   --shipment=ID              Specific shipment ID to test with
 *   --intent=INTENT_NAME       Test only specific intent (e.g., GET_SHIPMENT_STATUS)
 *   --instructions="text"      Custom instructions for the intent
 *   --verbose                  Show detailed logging including fragment details
 *   --show-html                Show the rendered HTML output
 *   --no-side-effects          Skip side effects like backoffice emails
 *
 * Examples:
 *   # Test GET_SHIPMENT_STATUS with auto-selected shipment
 *   node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS
 *
 *   # Test with custom instructions
 *   node src/core-agent/testing/test-processor-direct.js --intent=REQUEST_CAD_DOCUMENT --instructions="Please send me the CAD document for my shipment"
 *
 *   # Test PROCESS_DOCUMENT with specific shipment
 *   node src/core-agent/testing/test-processor-direct.js --shipment=123 --intent=PROCESS_DOCUMENT --verbose
 *
 *   # Test all intents for a shipment
 *   node src/core-agent/testing/test-processor-direct.js --shipment=123 --verbose
 */

const { AppModule } = require("../../../dist/app.module.js");
const { ContextIdFactory, NestFactory } = require("@nestjs/core");
const { DataSource } = require("typeorm");
const { generateRequest } = require("../../../dist/email/utils/generate-request");

// Import services and types
const { ShipmentContextService } = require("../../../dist/agent-context");
const {
  IntentHandlerRegistry
} = require("../../../dist/core-agent/services/intent-handler-registry.service");
const { ShipmentResponseService } = require("../../../dist/core-agent/services/shipment-response.service");
const { EmailService } = require("../../../dist/email/services/email.service");

// Entity imports
const { Organization, Shipment, Email, EmailStatus, FIND_ORGANIZATION_RELATIONS } = require("nest-modules");

// Default configuration
const DEFAULTS = {
  organizationId: "3", // Demo organization
  verbose: false,
  showHtml: false,
  sideEffects: false
};

// Test scenarios for different intents
const INTENT_TEST_SCENARIOS = {
  GET_SHIPMENT_STATUS: {
    intent: "GET_SHIPMENT_STATUS",
    description: "General inquiry on entry status",
    defaultInstructions: [
      "What is the status of my shipment?",
      "Has the shipment been released?",
      "How long will it take to clear customs?"
    ]
  },
  PROCESS_DOCUMENT: {
    intent: "PROCESS_DOCUMENT",
    description: "Process attached documents",
    defaultInstructions: ["Please process the attached documents", "Submit the entry with these documents"]
  },
  REQUEST_RUSH_PROCESSING: {
    intent: "REQUEST_RUSH_PROCESSING",
    description: "Rush shipment processing",
    defaultInstructions: [
      "Can you please clear the shipment quickly?",
      "The driver is at the border, please expedite"
    ]
  },
  DOCUMENTATION_COMING: {
    intent: "DOCUMENTATION_COMING",
    description: "Will provide documentation later",
    defaultInstructions: [
      "Shipment has moved in-bond, will be sending the manifest shortly",
      "Documents are coming soon"
    ]
  },
  UPDATE_SHIPMENT: {
    intent: "UPDATE_SHIPMENT",
    description: "Update shipment information",
    defaultInstructions: [
      "Update port code to 0123",
      "Change sublocation to 4567",
      "Update CCN to ABC123456789"
    ]
  },
  REQUEST_CAD_DOCUMENT: {
    intent: "REQUEST_CAD_DOCUMENT",
    description: "Request CAD document",
    defaultInstructions: [
      "Can you send me the CAD document?",
      "Please provide the customs accounting document"
    ]
  },
  REQUEST_RNS_PROOF: {
    intent: "REQUEST_RNS_PROOF",
    description: "Request RNS proof of release",
    defaultInstructions: [
      "Can you send me the RNS proof of release?",
      "I need the release notification document"
    ]
  },
  REQUEST_MANUAL_PROCESSING: {
    intent: "REQUEST_MANUAL_PROCESSING",
    description: "Request manual processing",
    defaultInstructions: ["Please cancel entry", "Can someone manually review this shipment?"]
  },
  REQUEST_HOLD_SHIPMENT: {
    intent: "REQUEST_HOLD_SHIPMENT",
    description: "Hold shipment processing",
    defaultInstructions: ["Please hold processing until approval", "Do not submit entry until CAD approval"]
  }
};

// Function to parse command line arguments
function parseCommandLineArguments(args) {
  const result = {
    organizationId: DEFAULTS.organizationId,
    shipmentId: null,
    intentFilter: null,
    customInstructions: null,
    sideEffects: DEFAULTS.sideEffects,
    verbose: DEFAULTS.verbose,
    showHtml: DEFAULTS.showHtml
  };

  const remainingArgs = args.slice(2);

  for (let i = 0; i < remainingArgs.length; i++) {
    const arg = remainingArgs[i];

    if (arg.startsWith("--org=")) {
      result.organizationId = arg.split("=")[1];
    } else if (arg.startsWith("--shipment=")) {
      result.shipmentId = parseInt(arg.split("=")[1]);
    } else if (arg.startsWith("--intent=")) {
      result.intentFilter = arg.split("=")[1];
    } else if (arg.startsWith("--instructions=")) {
      result.customInstructions = arg.split("=")[1];
    } else if (arg === "--no-side-effects") {
      result.sideEffects = false;
    } else if (arg === "--verbose") {
      result.verbose = true;
    } else if (arg === "--show-html") {
      result.showHtml = true;
    }
  }

  return result;
}

// Function to find suitable shipments for testing
async function findTestShipments(dataSource, organizationId, limit = 5) {
  console.log(`🔍 Finding suitable shipments for organization ${organizationId}...`);

  const shipments = await dataSource.query(
    `
    SELECT s.id, s."customsStatus", s."hblNumber", s."modeOfTransport",
           s."createDate", s."portCode", s."subLocation"
    FROM shipment s
    WHERE s."organizationId" = $1
      AND s."customsStatus" IS NOT NULL
    ORDER BY s."createDate" DESC
    LIMIT $2
  `,
    [organizationId, limit]
  );

  console.log(`✅ Found ${shipments.length} shipments:`);
  shipments.forEach((shipment, index) => {
    console.log(`   ${index + 1}. ID: ${shipment.id}, HBL: ${shipment.hblNumber || "N/A"}`);
    console.log(`      Status: ${shipment.customsStatus}, Mode: ${shipment.modeOfTransport}`);
    console.log(`      Port: ${shipment.portCode || "N/A"}, SubLocation: ${shipment.subLocation || "N/A"}`);
    console.log(`      Created: ${shipment.createDate}`);
    console.log("");
  });

  return shipments;
}

// Function to create a mock email for testing
async function createMockEmail(dataSource, organizationId, shipmentId, intent, instructions) {
  const mockEmail = {
    id: `test-email-${Date.now()}`,
    organizationId: parseInt(organizationId),
    subject: `Test Email - ${intent}`,
    fromEmail: "<EMAIL>",
    toEmail: "<EMAIL>",
    body: instructions.join("\n"),
    status: EmailStatus.PROCESSING,
    userIntents: [
      {
        intent: intent,
        instructions: instructions,
        attachments: []
      }
    ],
    createDate: new Date(),
    updateDate: new Date()
  };

  return mockEmail;
}

// Function to simulate the processor's intent processing logic
async function testProcessorLogic(
  intent,
  instructions,
  context,
  intentHandlerRegistry,
  shipmentResponseService,
  config
) {
  console.log(`\n🧪 Testing Processor Logic for Intent: ${intent}`);
  console.log(`📝 Instructions: ${instructions.slice(0, 2).join(", ")}...`);

  try {
    // Create validated intent object (same format as processor uses)
    const validatedIntent = {
      intent: intent,
      instructions: instructions,
      attachments: []
    };

    console.log(`⚙️  Processing intent through handler registry...`);
    const startTime = Date.now();

    // Get the handler (same as processor does)
    const handler = intentHandlerRegistry.getHandler(intent);
    if (!handler) {
      console.log(`❌ No handler found for intent: ${intent}`);
      return { success: false, error: "Handler not found" };
    }

    // Handle the intent (same as processor does)
    const fragments = await handler.handle(validatedIntent, context);
    const processingTime = Date.now() - startTime;

    console.log(`✅ Handler completed in ${processingTime}ms`);
    console.log(`📊 Generated ${fragments.length} response fragments`);

    // Show fragment details
    if (config.verbose && fragments.length > 0) {
      console.log(`📋 Fragment Details:`);
      fragments.forEach((fragment, index) => {
        console.log(
          `   ${index + 1}. Template: ${fragment.template}, Priority: ${fragment.priority || "default"}`
        );
        if (fragment.fragmentContext) {
          const contextKeys = Object.keys(fragment.fragmentContext);
          console.log(`      Fragment Context: ${contextKeys.join(", ")}`);
        }
      });
    }

    // Render fragments to HTML (same as processor does)
    console.log(`🎨 Rendering fragments to HTML...`);
    const responseHtml = await shipmentResponseService.renderFragments(fragments, context);

    // Show side effects
    if (context.sideEffects && Object.keys(context.sideEffects).length > 0) {
      console.log(`🔧 Side Effects Generated:`);
      Object.keys(context.sideEffects).forEach((key) => {
        if (context.sideEffects[key] && typeof context.sideEffects[key] === "object") {
          console.log(`   - ${key}: ${JSON.stringify(context.sideEffects[key], null, 2)}`);
        } else {
          console.log(`   - ${key}: ${context.sideEffects[key]}`);
        }
      });
    }

    // Show rendered response
    if (responseHtml && responseHtml.trim().length > 0) {
      if (config.showHtml) {
        console.log(`📧 Full HTML Response:`);
        console.log("=".repeat(50));
        console.log(responseHtml);
        console.log("=".repeat(50));
      } else {
        const truncatedHtml = responseHtml.replace(/<[^>]*>/g, "").substring(0, 300);
        console.log(`📧 Response Preview: ${truncatedHtml}...`);
      }
    } else {
      console.log(`⚠️  No response HTML generated`);
    }

    return {
      success: true,
      fragments: fragments.length,
      processingTime,
      hasResponse: responseHtml && responseHtml.trim().length > 0,
      sideEffects: Object.keys(context.sideEffects || {}),
      responseLength: responseHtml ? responseHtml.length : 0
    };
  } catch (error) {
    console.log(`❌ Error testing processor logic for ${intent}: ${error.message}`);
    if (config.verbose) {
      console.log(`💥 Stack trace: ${error.stack}`);
    }
    return { success: false, error: error.message };
  }
}

// Function to run all tests
async function runAllTests(config, app, dataSource) {
  console.log("🚀 Starting Direct Processor Tests");
  console.log("==================================\n");

  console.log("📝 Test Configuration:");
  console.log(`🏢 Organization ID: ${config.organizationId}`);
  console.log(`🚢 Shipment ID: ${config.shipmentId || "Auto-select"}`);
  console.log(`🎯 Intent Filter: ${config.intentFilter || "All intents"}`);
  console.log(`📝 Custom Instructions: ${config.customInstructions || "Use defaults"}`);
  console.log(`🔧 Side Effects: ${config.sideEffects ? "Enabled" : "Disabled"}`);
  console.log(`📊 Verbose: ${config.verbose ? "Yes" : "No"}`);
  console.log(`🌐 Show HTML: ${config.showHtml ? "Yes" : "No"}`);
  console.log("");

  try {
    // 1. Load organization
    const organization = await dataSource.manager.findOne(Organization, {
      where: { id: parseInt(config.organizationId) },
      relations: FIND_ORGANIZATION_RELATIONS
    });
    if (!organization) {
      throw new Error(`Organization not found: ${config.organizationId}`);
    }
    console.log(`✅ Loaded organization: ${organization.name}`);

    // 2. Create request context (same as processor does)
    const contextId = ContextIdFactory.create();
    const requestContext = generateRequest(null, organization);
    app.registerRequestByContextId(requestContext, contextId);

    // 3. Get services (same as processor does)
    const shipmentContextService = await app.resolve(ShipmentContextService, contextId);
    const intentHandlerRegistry = await app.resolve(IntentHandlerRegistry, contextId);
    const shipmentResponseService = await app.resolve(ShipmentResponseService, contextId);
    const emailService = await app.resolve(EmailService, contextId);

    console.log(`✅ Resolved core-agent services`);

    // 4. Find or use specified shipment
    let shipment;
    if (config.shipmentId) {
      shipment = await dataSource.manager.findOne(Shipment, {
        where: { id: config.shipmentId, organizationId: parseInt(config.organizationId) }
      });
      if (!shipment) {
        throw new Error(`Shipment not found: ${config.shipmentId}`);
      }
      console.log(`✅ Using specified shipment: ${shipment.id}`);
    } else {
      const shipments = await findTestShipments(dataSource, parseInt(config.organizationId), 1);
      if (shipments.length === 0) {
        throw new Error(`No shipments found for organization ${config.organizationId}`);
      }
      shipment = shipments[0];
      console.log(`✅ Auto-selected shipment: ${shipment.id}`);
    }

    // 5. Build shipment context (same as processor does)
    console.log(`🔧 Building shipment context...`);
    const context = await shipmentContextService.buildContext(shipment.id, parseInt(config.organizationId));

    // 5b. Inject services into context (same as processor does)
    shipmentContextService.injectServices(context, {
      emailService: emailService
    });
    console.log(`✅ Built context for shipment ${shipment.id}`);

    // 6. Run tests
    const testResults = {};
    const intentsToTest = config.intentFilter ? [config.intentFilter] : Object.keys(INTENT_TEST_SCENARIOS);

    console.log(`\n🧪 Running processor tests for ${intentsToTest.length} intent(s)...\n`);

    for (const intentName of intentsToTest) {
      const scenario = INTENT_TEST_SCENARIOS[intentName];
      if (!scenario) {
        console.log(`⚠️  Unknown intent: ${intentName}`);
        continue;
      }

      // Use custom instructions if provided, otherwise use defaults
      const instructions = config.customInstructions
        ? [config.customInstructions]
        : scenario.defaultInstructions;

      const result = await testProcessorLogic(
        intentName,
        instructions,
        context,
        intentHandlerRegistry,
        shipmentResponseService,
        config
      );

      testResults[intentName] = result;
    }

    // 7. Show summary
    console.log(`\n📊 Processor Test Results Summary`);
    console.log("=================================");

    let successCount = 0;
    let totalFragments = 0;
    let totalProcessingTime = 0;
    let totalResponseLength = 0;

    Object.entries(testResults).forEach(([intentName, result]) => {
      const status = result.success ? "✅ PASS" : "❌ FAIL";
      console.log(`${status} ${intentName}`);

      if (result.success) {
        successCount++;
        totalFragments += result.fragments || 0;
        totalProcessingTime += result.processingTime || 0;
        totalResponseLength += result.responseLength || 0;

        if (config.verbose) {
          console.log(`     Fragments: ${result.fragments}, Time: ${result.processingTime}ms`);
          console.log(`     Response: ${result.hasResponse ? "Yes" : "No"} (${result.responseLength} chars)`);
          console.log(
            `     Side Effects: ${result.sideEffects.length > 0 ? result.sideEffects.join(", ") : "None"}`
          );
        }
      } else {
        console.log(`     Error: ${result.error}`);
      }
    });

    console.log(`\n📈 Overall Results:`);
    console.log(`   Tests Passed: ${successCount}/${Object.keys(testResults).length}`);
    console.log(`   Total Fragments: ${totalFragments}`);
    console.log(`   Total Processing Time: ${totalProcessingTime}ms`);
    console.log(`   Total Response Length: ${totalResponseLength} characters`);
    if (successCount > 0) {
      console.log(`   Average Time per Intent: ${Math.round(totalProcessingTime / successCount)}ms`);
      console.log(`   Average Response Length: ${Math.round(totalResponseLength / successCount)} characters`);
    }

    return testResults;
  } catch (error) {
    console.error(`💥 Test execution failed: ${error.message}`);
    if (config.verbose) {
      console.error(`Stack trace: ${error.stack}`);
    }
    throw error;
  }
}

// Main bootstrap function
async function bootstrap() {
  const config = parseCommandLineArguments(process.argv);
  let app;

  try {
    // Create NestJS application context
    console.log("🔧 Initializing NestJS application context...");
    app = await NestFactory.createApplicationContext(AppModule, {
      logger: config.verbose ? ["log", "error", "warn", "debug"] : ["error"]
    });

    const dataSource = await app.get(DataSource);
    console.log("✅ NestJS context initialized\n");

    // Run all tests
    await runAllTests(config, app, dataSource);

    console.log("\n🎉 Direct processor testing completed successfully!");
  } catch (error) {
    console.error(`💥 Bootstrap failed: ${error.message}`);
    process.exit(1);
  } finally {
    // Ensure app is always closed
    if (app) {
      try {
        await app.close();
      } catch (closeError) {
        console.error("Error closing app:", closeError.message);
      }
    }
    process.exit(0);
  }
}

// Handle process termination gracefully
process.on("SIGINT", () => {
  console.log("\nReceived SIGINT, shutting down gracefully...");
  process.exit(0);
});

process.on("SIGTERM", () => {
  console.log("\nReceived SIGTERM, shutting down gracefully...");
  process.exit(0);
});

// Show usage if no arguments provided or help requested
if (process.argv.length <= 2 || process.argv.includes("--help") || process.argv.includes("-h")) {
  console.log("Direct Processor Testing Script Usage:");
  console.log("=====================================");
  console.log("");
  console.log("This script directly tests the handle-request-message.processor.ts logic");
  console.log("by feeding different intents and shipment data to see what templates");
  console.log("and output they produce.");
  console.log("");
  console.log("Commands:");
  console.log("  --org=ID                   Organization ID (default: 3)");
  console.log("  --shipment=ID              Specific shipment ID to test with");
  console.log("  --intent=INTENT_NAME       Test only specific intent");
  console.log('  --instructions="text"      Custom instructions for the intent');
  console.log("  --verbose                  Show detailed logging including fragments");
  console.log("  --show-html                Show the full rendered HTML output");
  console.log("  --no-side-effects          Skip side effects like backoffice emails");
  console.log("");
  console.log("Available Intents:");
  Object.keys(INTENT_TEST_SCENARIOS).forEach((intent) => {
    console.log(`  - ${intent}`);
  });
  console.log("");
  console.log("Examples:");
  console.log("  # Test GET_SHIPMENT_STATUS with auto-selected shipment");
  console.log("  node src/core-agent/testing/test-processor-direct.js --intent=GET_SHIPMENT_STATUS");
  console.log("");
  console.log("  # Test with custom instructions");
  console.log(
    '  node src/core-agent/testing/test-processor-direct.js --intent=REQUEST_CAD_DOCUMENT --instructions="Please send me the CAD document for my shipment"'
  );
  console.log("");
  console.log("  # Test PROCESS_DOCUMENT with specific shipment and verbose output");
  console.log(
    "  node src/core-agent/testing/test-processor-direct.js --shipment=123 --intent=PROCESS_DOCUMENT --verbose"
  );
  console.log("");
  console.log("  # Test all intents for a shipment with HTML output");
  console.log("  node src/core-agent/testing/test-processor-direct.js --shipment=123 --verbose --show-html");
  console.log("");
  console.log("  # Test with specific organization");
  console.log("  node src/core-agent/testing/test-processor-direct.js --org=1 --intent=GET_SHIPMENT_STATUS");
  process.exit(0);
}

bootstrap();
