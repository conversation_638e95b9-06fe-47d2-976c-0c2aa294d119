{# Consolidated Validation Issues Template
   Shows missing documents and fields when present
   
   Data dependencies:
   - showValidationIssues: Boolean flag to control display
   - validationIssues.missingDocuments: Array of missing document types
   - validationIssues.missingFields: Array of missing field types
   
   Missing document types: 'CI_PL', 'HBL', 'AN_EMF'
   Missing field types: 'weight', 'port_code', 'ccn', 'ogd_filing'
#}
{% if showValidationIssues %}
  {# Missing documents section #}
  {% if validationIssues and validationIssues.missingDocuments and validationIssues.missingDocuments.length > 0 %}
    {% for doc in validationIssues.missingDocuments %}
      {% if doc == 'CI_PL' %}CI & PL: <strong>Missing</strong><br/>{% endif %}
      {% if doc == 'HBL' %}HBL: <strong>Missing</strong><br/>{% endif %}
      {% if doc == 'AN_EMF' %}AN/EMF: <strong>Missing</strong><br/>{% endif %}
    {% endfor %}
  {% endif %}
  
  {# Missing fields section - single line with spaces between #}
  {% if validationIssues and validationIssues.missingFields and validationIssues.missingFields.length > 0 %}
    {% for field in validationIssues.missingFields %}
      {%- if field == 'weight' %}Weight: <strong>missing</strong> {% endif -%}
      {%- if field == 'port_code' %}Port code: <strong>missing</strong> {% endif -%}
      {%- if field == 'ccn' %}CCN: <strong>missing</strong> {% endif -%}
      {%- if field == 'ogd_filing' %}OGD filing: <strong>Pending</strong> {% endif -%}
    {% endfor %}<br/>
  {% endif %}
{% endif %}