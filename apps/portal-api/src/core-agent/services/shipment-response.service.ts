import { Injectable, Logger } from "@nestjs/common";
import { TemplateManagerService } from "nest-modules";
import { ResponseFragment } from "../types/response-fragment.types";
import { ShipmentContext, ShipmentContextWithServices } from "../../agent-context";
import { TEMPLATE_ORDER } from "../constants/templates";

/**
 * Layer 3: Response Service
 *
 * Provides pure rendering of response fragments using templates.
 * Handles deduplication, sorting, and consolidation of fragments
 * into final shipment responses.
 */
@Injectable()
export class ShipmentResponseService {
  private readonly logger = new Logger(ShipmentResponseService.name);

  constructor(private readonly templateManagerService: TemplateManagerService) {}

  /**
   * Render fragments into a consolidated response.
   *
   * @param fragments - Array of response fragments to render
   * @param context - Full shipment context for template rendering
   * @returns Consolidated HTML response
   */
  async renderFragments(fragments: ResponseFragment[], context: ShipmentContext): Promise<string> {
    const startTime = Date.now();

    this.logger.log(
      `Rendering ${fragments.length} fragments for shipment ${context.shipment.id}: ${fragments.map((f) => f.template).join(", ")}`
    );

    try {
      // 1. Deduplicate by template name
      const uniqueFragments = this.deduplicateFragments(fragments);
      this.logger.debug(`After deduplication: ${uniqueFragments.length} fragments`);

      // 2. Sort by priority and predefined order
      const sortedFragments = this.sortFragments(uniqueFragments);

      // 3. Render each template with merged context
      const renderedParts: string[] = [];

      for (const fragment of sortedFragments) {
        try {
          const fragmentContext = this.mergeContexts(context, fragment.fragmentContext);
          const rendered = await this.renderSingleFragment(fragment, fragmentContext);

          if (rendered.trim()) {
            renderedParts.push(rendered);
          }
        } catch (error) {
          this.logger.error(
            `Failed to render fragment '${fragment.template}': ${error.message}`,
            error.stack
          );

          // Skip this fragment entirely - other fragments can still be processed
          // If no fragments render successfully, the processor will handle escalation
        }
      }

      const consolidatedResponse = this.joinFragments(renderedParts);
      const duration = Date.now() - startTime;

      this.logger.log(`Rendered ${renderedParts.length} fragments in ${duration}ms`);

      // Always prepend greeting to every email reply
      const responseWithGreeting = `<p>Hello,</p>\n${consolidatedResponse}`;
      return responseWithGreeting;
    } catch (error) {
      this.logger.error(
        `Critical error in fragment rendering for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );

      return this.renderFallbackResponse(error.message);
    }
  }

  /**
   * Remove duplicate fragments by template name with special handling for stackable templates.
   * For main-messages: merge mainMessages arrays from all handlers
   * For other templates: use first fragment (they contain identical shipment-specific data)
   */
  private deduplicateFragments(fragments: ResponseFragment[]): ResponseFragment[] {
    const templateMap = new Map<string, ResponseFragment>();

    for (const fragment of fragments) {
      const existingFragment = templateMap.get(fragment.template);

      if (!existingFragment) {
        // First fragment of this template type
        templateMap.set(fragment.template, fragment);
      } else if (fragment.template === "consolidated/main-messages") {
        // Special handling for main-messages: merge the mainMessages arrays
        const existingMessages = Array.isArray(existingFragment.fragmentContext?.mainMessages)
          ? existingFragment.fragmentContext.mainMessages
          : [];
        const newMessages = Array.isArray(fragment.fragmentContext?.mainMessages)
          ? fragment.fragmentContext.mainMessages
          : [];
        const mergedMessages = [...existingMessages, ...newMessages];

        this.logger.debug(
          `Merging main-messages: ${existingMessages.length} existing + ${newMessages.length} new = ${mergedMessages.length} total`
        );

        // Update the existing fragment with merged messages
        templateMap.set(fragment.template, {
          ...existingFragment,
          fragmentContext: {
            ...existingFragment.fragmentContext,
            mainMessages: mergedMessages
          }
        });
      }
      // For all other templates, keep the first fragment (ignore subsequent ones)
    }

    return Array.from(templateMap.values());
  }

  /**
   * Sort fragments by priority and predefined template order.
   */
  private sortFragments(fragments: ResponseFragment[]): ResponseFragment[] {
    return fragments.sort((a, b) => {
      // First sort by explicit priority if provided
      if (a.priority !== undefined || b.priority !== undefined) {
        const aPriority = a.priority ?? 999;
        const bPriority = b.priority ?? 999;

        if (aPriority !== bPriority) {
          return aPriority - bPriority;
        }
      }

      // Then sort by predefined template order
      const indexA = TEMPLATE_ORDER.indexOf(a.template as any);
      const indexB = TEMPLATE_ORDER.indexOf(b.template as any);

      // Templates not in the order list go to the end
      const orderA = indexA === -1 ? 999 + TEMPLATE_ORDER.length : indexA;
      const orderB = indexB === -1 ? 999 + TEMPLATE_ORDER.length : indexB;

      return orderA - orderB;
    });
  }

  /**
   * Merge fragment-specific context with main shipment context.
   */
  private mergeContexts(
    mainContext: ShipmentContext,
    fragmentContext?: Record<string, any>
  ): ShipmentContext {
    if (!fragmentContext) {
      return mainContext;
    }

    // Merge fragment context while preserving main context structure
    return {
      ...mainContext,
      ...fragmentContext,
      // Preserve core properties from fragmentContext if they exist, otherwise use mainContext
      // This allows handlers to override these with updated data (e.g., refreshed shipment)
      shipment: fragmentContext.shipment || mainContext.shipment,
      compliance: fragmentContext.compliance || mainContext.compliance,
      organization: fragmentContext.organization || mainContext.organization
    };
  }

  /**
   * Render a single fragment using the template manager.
   */
  private async renderSingleFragment(fragment: ResponseFragment, context: ShipmentContext): Promise<string> {
    try {
      // Remove sensitive data from context before rendering
      const safeContext = this.sanitizeContextForTemplate(context);

      // Merge with fragment-specific context if provided
      const mergedContext = this.mergeContexts(safeContext, fragment.fragmentContext);

      // Debug logging for specific template - AFTER merging context
      if (fragment.template === "core-agent/fragments/system/enhanced-document-processing-acknowledgment") {
        const contextData = mergedContext as any; // Cast to access dynamic properties
        this.logger.debug(
          `🎨 TEMPLATE CONTEXT DEBUG for ${fragment.template}: ` +
            `processedDocuments=${contextData.processedDocuments?.length || "undefined"}, ` +
            `hasAllRequiredDocuments=${contextData.hasAllRequiredDocuments}, ` +
            `shipment.customsStatus=${contextData.shipment?.customsStatus}`
        );
      }

      const result = await this.templateManagerService.renderTemplate(fragment.template, mergedContext);
      return result;
    } catch (error) {
      this.logger.error(`Failed to render fragment ${fragment.template}:`, error);
      throw error;
    }
  }

  /**
   * Remove sensitive data and service instances from context before template rendering.
   */
  private sanitizeContextForTemplate(context: ShipmentContext | ShipmentContextWithServices): any {
    // Check if context has _services property and destructure accordingly
    const sanitizedContext = "_services" in context ? (({ _services, ...rest }) => rest)(context) : context;

    return {
      ...sanitizedContext,
      // Escape any HTML in user-provided content
      shipment: {
        ...sanitizedContext.shipment,
        hblNumber: this.escapeHtml(sanitizedContext.shipment.hblNumber),
        cargoControlNumber: this.escapeHtml(sanitizedContext.shipment.cargoControlNumber)
      }
    };
  }

  /**
   * Escape HTML characters to prevent injection attacks.
   */
  private escapeHtml(text: string | null | undefined): string | null {
    if (!text) return text as null;

    return text
      .replace(/&/g, "&amp;")
      .replace(/</g, "&lt;")
      .replace(/>/g, "&gt;")
      .replace(/"/g, "&quot;")
      .replace(/'/g, "&#x27;");
  }

  /**
   * Render a fallback response when critical errors occur.
   */
  private renderFallbackResponse(errorMessage: string): string {
    this.logger.error(`Using fallback response due to critical error: ${errorMessage}`);

    return `<p>Hello,</p>
      <p>Thank you for your inquiry. We've received your message and will get back to you shortly.</p>
    `;
  }

  /**
   * Get system diagnostics for monitoring and debugging.
   */
  getSystemDiagnostics(): {
    templateManagerAvailable: boolean;
    lastRenderTime?: Date;
    totalRendersToday: number;
  } {
    return {
      templateManagerAvailable: Boolean(this.templateManagerService),
      // TODO: Add metrics tracking
      totalRendersToday: 0
    };
  }

  /**
   * Validate that a template exists and can be rendered.
   */
  async validateTemplate(templateName: string, sampleContext: any): Promise<boolean> {
    try {
      await this.templateManagerService.renderTemplate(templateName, sampleContext);
      return true;
    } catch (error) {
      this.logger.warn(`Template validation failed for '${templateName}': ${error.message}`);
      return false;
    }
  }

  /**
   * Join fragments with appropriate spacing for HTML content.
   */
  private joinFragments(fragments: string[]): string {
    if (fragments.length === 0) return "";
    if (fragments.length === 1) return fragments[0];

    // Check if content appears to be HTML
    const hasHtmlContent = fragments.some(
      (fragment) =>
        fragment.includes("<p") ||
        fragment.includes("<div") ||
        fragment.includes("<ul") ||
        fragment.includes("<li")
    );

    if (hasHtmlContent) {
      // For HTML content, join with line breaks between blocks
      return fragments.join("\n");
    } else {
      // For plain text content, use double line breaks
      return fragments.join("\n\n");
    }
  }

  /**
   * Build standardized compliance details for template context
   * @param context - ShipmentContext containing missing fields analysis
   * @returns Standardized compliance details object
   */
  buildComplianceDetails(context: ShipmentContext): Partial<Record<string, any>> {
    const formattedFields = context.missingFieldsAnalysis?.formattedMissingFields || [];
    const missingFields = formattedFields.filter(
      (field) => field.includes("**missing**") || field.includes("**Pending**")
    );

    return {
      missingFieldsFormatted: formattedFields.join("\n"),
      complianceDetails: {
        hasMissingFields: missingFields.length > 0,
        missingFieldsList: missingFields,
        formattedMissingFields: formattedFields
      }
    };
  }
}
