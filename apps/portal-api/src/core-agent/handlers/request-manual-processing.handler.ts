import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { EmailService } from "../../email/services/email.service";

/**
 * <PERSON>les requests for manual processing of shipments.
 * Sends backoffice alerts and provides acknowledgment to users.
 */
@Injectable()
export class RequestManualProcessingHandler extends BaseIntentHandler {
  constructor(private readonly emailService: EmailService) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_MANUAL_PROCESSING" as const,
    description: "User is requesting manual processing or intervention for their shipment",
    examples: [
      "Please cancel entry",
      "Can someone manually review this?",
      "I need manual processing",
      "Please handle this manually",
      "Cancel the entry submission"
    ],
    keywords: ["manual", "cancel", "review", "intervention", "manually", "cancel entry"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling REQUEST_MANUAL_PROCESSING for shipment ${context.shipment?.id || "N/A"} with ${instructions.length} requests`
    );

    if (instructions.length === 0) {
      this.logger.error("Cannot process manual processing request: no instructions provided");
      throw new Error("No instructions provided for manual processing request");
    }

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(validatedIntent, context);
      this.logger.debug(`[REQUEST_MANUAL_PROCESSING] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(
        `[REQUEST_MANUAL_PROCESSING] Consolidated options: ${JSON.stringify(consolidatedOptions)}`
      );

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[REQUEST_MANUAL_PROCESSING] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment?.id || "N/A"}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to process manual processing request for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Build main messages array for consolidated templates
   * MIGRATION: Replaces manual processing fragment logic
   */
  private async buildMainMessages(
    validatedIntent: ValidatedIntent,
    context: ShipmentContext
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];
    const instructions = this.extractInstructions(validatedIntent);

    // Send backoffice alert for manual processing request
    const alertData = await this.sendBackofficeAlert(
      "Manual Processing Request",
      context.shipment?.id || 0,
      instructions,
      this.emailService,
      context.organization.id
    );

    // Build manual processing message
    const manualProcessingMessage = this.buildManualProcessingMessage(context, instructions, alertData);
    mainMessages.push({
      type: manualProcessingMessage.type,
      priority: 1,
      attachments: manualProcessingMessage.attachments
    });

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContext): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // Manual processing requests don't typically need validation issues shown
    const options = {
      showValidationIssues: false,
      showDocumentStatus: false,
      showAdditionalValidation: false,
      validationIssues: null,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[REQUEST_MANUAL_PROCESSING] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Build manual processing message using template-based approach
   */
  private buildManualProcessingMessage(
    context: ShipmentContext,
    instructions: string[],
    alertData: any
  ): {
    type: string;
    attachments: any;
  } {
    return {
      type: "manual-processing-messages",
      attachments: {
        acknowledgment:
          "We've received your request for manual processing and our team will review this accordingly. Thank you.",
        instructions: instructions,
        backofficeAlerts: alertData.backofficeAlerts,
        directlyAsked: { manual_processing: true }
      }
    };
  }
}
