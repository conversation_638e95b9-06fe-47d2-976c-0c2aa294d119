import { Injectable, Inject } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent, FragmentContext } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { Shipment } from "nest-modules";
import { RnsProofService } from "../../email/services/rns-proof-service";
import { RNSStatusChangeEmailSender } from "../../shipment/senders/rns-status-change-email.sender";
import { ImporterService } from "../../importer/importer.service";
import { ShipmentResponseService } from "../services/shipment-response.service";

/**
 * <PERSON><PERSON> requests for RNS (Release Notification System) proof of release documents.
 * Evaluates business rules and generates RNS proof documents when possible.
 */
@Injectable()
export class RequestRNSProofHandler extends BaseIntentHandler {
  constructor(
    private readonly rnsProofService: RnsProofService,
    private readonly rnsStatusChangeEmailSender: RNSStatusChangeEmailSender,
    private readonly importerService: ImporterService,
    @Inject(ShipmentResponseService) private readonly shipmentResponseService: ShipmentResponseService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_RNS_PROOF" as const,
    description: "User is requesting RNS proof of release or release notification document",
    examples: [
      "Can you send me the RNS proof of release?",
      "I need the release notification",
      "Please provide proof of release",
      "Send me the RNS document",
      "Do you have the release paperwork?"
    ],
    keywords: ["rns", "proof of release", "release notification", "release document", "release paperwork"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(`[REQUEST_RNS_PROOF] Handling request for shipment ${context.shipment?.id || "N/A"}`);

    if (!context.shipment) {
      this.logger.error("Cannot generate RNS proof: no shipment found in context");
      throw new Error("No shipment found for RNS proof generation");
    }

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(context);
      this.logger.debug(`[REQUEST_RNS_PROOF] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(`[REQUEST_RNS_PROOF] Consolidated options: ${JSON.stringify(consolidatedOptions)}`);

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[REQUEST_RNS_PROOF] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment.id}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to generate RNS proof of release for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Build main messages array for consolidated templates
   * MIGRATION: Replaces RNS response fragment logic
   */
  private async buildMainMessages(
    context: ShipmentContext
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];

    // Generate RNS proof content if available
    let rnsData = undefined;
    if (context.smartTemplateContext.rnsDocumentAvailable) {
      try {
        const rnsContent = await this.generateRNSProofContent(
          context.shipment,
          context.smartTemplateContext,
          context.organization.id
        );
        rnsData = rnsContent.rnsProofData;
        this.logger.debug(`[REQUEST_RNS_PROOF] Generated RNS proof data for shipment ${context.shipment.id}`);
      } catch (error) {
        this.logger.error(`[REQUEST_RNS_PROOF] Failed to generate RNS proof data: ${error.message}`);
        // Continue without RNS data - template will handle the error state
      }
    } else {
      this.logger.debug(`[REQUEST_RNS_PROOF] RNS document not available for shipment ${context.shipment.id}`);
    }

    // Build RNS message using template-based approach
    const rnsMessage = this.buildRnsMessage(context, rnsData);
    mainMessages.push({
      type: rnsMessage.type,
      priority: 1,
      attachments: rnsMessage.attachments
    });

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContext): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // For RNS requests, show validation issues if the shipment has pending statuses
    const shouldShowValidationIssues = ["pending-commercial-invoice", "pending-confirmation"].includes(
      context.shipment.customsStatus
    );

    // Build validation issues if needed
    let validationIssues = null;
    if (shouldShowValidationIssues) {
      validationIssues = this.buildValidationIssues(context);
    }

    const options = {
      showValidationIssues: shouldShowValidationIssues && !!validationIssues,
      showDocumentStatus: shouldShowValidationIssues, // Show document status for pending statuses
      showAdditionalValidation: false, // Not used for RNS requests
      validationIssues,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[REQUEST_RNS_PROOF] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Generate RNS proof content and return as fragment context data
   */
  private async generateRNSProofContent(
    shipment: Shipment,
    smartTemplateContext: any,
    organizationId: number
  ): Promise<Partial<FragmentContext>> {
    // Use the RnsProofService to get the RNS data
    const rnsProofData = await this.rnsProofService.getRNSProofOfRelease(shipment);

    if (!rnsProofData.isReleased || !rnsProofData.rnsResponse) {
      throw new Error("No release information found for this shipment");
    }

    // Get organization importer
    const importersResponse = await this.importerService.getImporters({
      organizationId: organizationId,
      limit: 1
    });
    const organizationImporter =
      importersResponse.importers.length > 0 ? importersResponse.importers[0] : null;

    // Use the existing prepareRNSStatusEmail method to generate the same email content
    const rnsEmailDto = this.rnsStatusChangeEmailSender.prepareRNSStatusEmail(
      shipment,
      organizationImporter,
      rnsProofData.rnsResponse,
      null, // no previous email for this context
      [] // no recipients needed for this context
    );

    // Format the email text as HTML for the response
    const rnsProofContent = `
      <div style="font-family: monospace; white-space: pre-line; background-color: #f5f5f5; padding: 15px; border: 1px solid #ddd; border-radius: 4px;">
        <strong>RNS PROOF OF RELEASE</strong>

        ${rnsEmailDto.text}
      </div>
    `.trim();

    this.logger.log(`Successfully generated RNS proof of release for shipment ${shipment.id}.`);

    return {
      rnsProofData: {
        content: rnsProofContent,
        releaseDate: rnsProofData.releaseDate
      },
      rnsProofContent: rnsProofContent
    };
  }
}
