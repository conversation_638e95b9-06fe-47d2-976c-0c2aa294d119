import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";

@Injectable()
export class AcknowledgeDocumentsHandler extends BaseIntentHandler {
  readonly classificationMeta: IntentClassificationMeta = {
    intent: "ACKNOWLEDGE_DOCUMENTS" as const,
    description: "Acknowledging receipt of documents from importer",
    examples: [
      "Here are the missing documents",
      "I'm sending you the commercial invoice",
      "Attached is the packing list",
      "Documents are attached",
      "Please find the requested documents attached"
    ],
    keywords: ["attached", "here is", "sending", "documents", "invoice", "packing list", "find attached"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    this.logger.log(`Handling ACKNOWLEDGE_DOCUMENTS for shipment ${context.shipment?.id || "N/A"}`);

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(validatedIntent, context);
      this.logger.debug(`[ACKNOWLEDGE_DOCUMENTS] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(
        `[ACKNOWLEDGE_DOCUMENTS] Consolidated options: ${JSON.stringify(consolidatedOptions)}`
      );

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[ACKNOWLEDGE_DOCUMENTS] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment?.id || "N/A"}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to process document acknowledgment for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Build main messages array for consolidated templates
   * MIGRATION: Replaces document acknowledgment fragment logic
   */
  private async buildMainMessages(
    validatedIntent: ValidatedIntent,
    context: ShipmentContext
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];

    // Build document acknowledgment message
    const acknowledgeMessage = this.buildAcknowledgeMessage(context);
    mainMessages.push({
      type: acknowledgeMessage.type,
      priority: 1,
      attachments: acknowledgeMessage.attachments
    });

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContext): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // Document acknowledgment doesn't show document status (as per original logic)
    const options = {
      showValidationIssues: false,
      showDocumentStatus: false,
      showAdditionalValidation: false,
      validationIssues: null,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[ACKNOWLEDGE_DOCUMENTS] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Build acknowledge documents message using template-based approach
   */
  private buildAcknowledgeMessage(context: ShipmentContext): {
    type: string;
    attachments: any;
  } {
    return {
      type: "acknowledge-documents-messages",
      attachments: {
        allDocsReceived: true,
        transportMode: context.smartTemplateContext.transportMode,
        shipment: context.shipment,
        shipmentIdentifiers: context.shipmentIdentifiers,
        smartTemplateContext: context.smartTemplateContext,
        directlyAsked: { document_acknowledgment: true }
      }
    };
  }
}
