import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";

/**
 * Handles user notifications about incoming documentation.
 * Provides acknowledgment and sets expectations.
 */
@Injectable()
export class DocumentationComingHandler extends BaseIntentHandler {
  readonly classificationMeta: IntentClassificationMeta = {
    intent: "DOCUMENTATION_COMING" as const,
    description: "User is notifying that additional documents are coming or will be sent soon",
    examples: [
      "I'll send you the missing documents shortly",
      "Documents are coming",
      "Paperwork will be forwarded tomorrow",
      "Additional documentation incoming"
    ],
    keywords: ["coming", "sending", "forward", "incoming", "tomorrow", "shortly", "soon"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    this.logger.log(`Handling DOCUMENTATION_COMING for shipment ${context.shipment?.id || "N/A"}`);

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(validatedIntent, context);
      this.logger.debug(`[DOCUMENTATION_COMING] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(
        `[DOCUMENTATION_COMING] Consolidated options: ${JSON.stringify(consolidatedOptions)}`
      );

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[DOCUMENTATION_COMING] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment?.id || "N/A"}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to process documentation coming for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Build main messages array for consolidated templates
   * MIGRATION: Replaces documentation coming fragment logic
   */
  private async buildMainMessages(
    validatedIntent: ValidatedIntent,
    context: ShipmentContext
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];

    // Build documentation coming message
    const documentationComingMessage = this.buildDocumentationComingMessage(context);
    mainMessages.push({
      type: documentationComingMessage.type,
      priority: 1,
      attachments: documentationComingMessage.attachments
    });

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContext): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // Documentation coming typically includes shipment details (priority 2)
    const options = {
      showValidationIssues: false,
      showDocumentStatus: true,
      showAdditionalValidation: false,
      validationIssues: null,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[DOCUMENTATION_COMING] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Build documentation coming message using template-based approach
   */
  private buildDocumentationComingMessage(context: ShipmentContext): {
    type: string;
    attachments: any;
  } {
    return {
      type: "documentation-coming-messages",
      attachments: {
        acknowledgment:
          "Thank you for letting us know. We'll keep an eye out for the additional documentation.",
        directlyAsked: { documentation_coming: true }
      }
    };
  }
}
