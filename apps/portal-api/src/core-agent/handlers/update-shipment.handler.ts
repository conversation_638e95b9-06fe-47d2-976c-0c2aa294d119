import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "@/agent-context";
import { ShipmentServicesAdapter } from "@/agent-context/services/shipment-services.adapter";
import { ShipmentFieldExtractionService } from "../services/shipment-field-extraction.service";

/**
 * Handles shipment information updates (Port Code, Sublocation Code, Cargo Control Number).
 * Validates and updates shipment information through proper APIs.
 */
@Injectable()
export class UpdateShipmentHandler extends BaseIntentHandler {
  constructor(
    private readonly shipmentServicesAdapter: ShipmentServicesAdapter,
    private readonly shipmentFieldExtractionService: ShipmentFieldExtractionService
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "UPDATE_SHIPMENT" as const,
    description:
      "User is requesting to update specific shipment information like port code, sublocation code, or cargo control number",
    examples: [
      "Update port code to 0123",
      "Change sublocation to 4567",
      "Update CCN to ABC123456789",
      "Shipment in-bond use attached manifest to clear",
      "Update cargo control number",
      "Change the port code"
    ],
    keywords: ["update", "change", "port", "sublocation", "ccn", "cargo control", "manifest", "in-bond"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling UPDATE_SHIPMENT for shipment ${context.shipment?.id || "N/A"} with ${instructions.length} update requests`
    );

    if (!context.shipment) {
      this.logger.warn("Cannot update shipment information as no shipment is linked.");
      return this.createErrorFragments(
        "We could not update the shipment information as we couldn't find the associated shipment from this email thread."
      );
    }

    if (instructions.length === 0) {
      this.logger.warn("No update instructions provided for shipment update request");
      return this.createErrorFragments(
        "No specific update instructions were provided. Please specify what information you'd like to update."
      );
    }

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(validatedIntent, context);
      this.logger.debug(`[UPDATE_SHIPMENT] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(`[UPDATE_SHIPMENT] Consolidated options: ${JSON.stringify(consolidatedOptions)}`);

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[UPDATE_SHIPMENT] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment?.id || "N/A"}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to update shipment ${context.shipment.id}. Error: ${error.message}`,
        error.stack
      );

      return this.createErrorFragments(
        "We encountered an error while updating the shipment information. Our team will review this shortly."
      );
    }
  }

  /**
   * Validate update data according to business rules.
   * The LLM extraction already handles format validation, but we add additional business rule checks.
   */
  private validateUpdateData(updateData: Record<string, any>): { isValid: boolean; errors: string[] } {
    const errors: string[] = [];

    // Validate port code format: ^0[0-9]{3}$ (double-check LLM extraction)
    if (updateData.portCode && !/^0\d{3}$/.test(updateData.portCode)) {
      errors.push("Port code must be in format 0XXX (e.g., 0123)");
    }

    // Validate sublocation format: ^[0-9]{4}$ (double-check LLM extraction)
    if (updateData.subLocation && !/^\d{4}$/.test(updateData.subLocation)) {
      errors.push("Sublocation code must be 4 digits (e.g., 1234)");
    }

    // Validate CCN format (basic validation)
    if (updateData.cargoControlNumber && updateData.cargoControlNumber.length < 3) {
      errors.push("Cargo Control Number must be at least 3 characters");
    }

    return {
      isValid: errors.length === 0,
      errors
    };
  }

  /**
   * Build main messages array for consolidated templates
   * MIGRATION: Handles shipment update processing logic
   */
  private async buildMainMessages(
    validatedIntent: ValidatedIntent,
    context: ShipmentContext
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];
    const instructions = this.extractInstructions(validatedIntent);

    // Extract update information from instructions using LLM
    const instructionsText = instructions.join(" ");
    const updateData = await this.shipmentFieldExtractionService.extractShipmentFields(instructionsText);

    if (!updateData || Object.keys(updateData).length === 0) {
      this.logger.warn("Could not extract valid update data from instructions");
      const errorMessage = this.buildUpdateErrorMessage(
        "We could not identify the specific information to update. Please provide clear update instructions with the new values."
      );
      mainMessages.push({
        type: errorMessage.type,
        priority: 1,
        attachments: errorMessage.attachments
      });
      return mainMessages;
    }

    this.logger.log(
      `Attempting to update shipment ${context.shipment.id} with data: ${JSON.stringify(updateData)}`
    );

    // Validate the update data
    const validationResult = this.validateUpdateData(updateData);
    if (!validationResult.isValid) {
      this.logger.warn(`Update data validation failed: ${validationResult.errors.join(", ")}`);
      const errorMessage = this.buildUpdateErrorMessage(
        `The update information provided is not valid: ${validationResult.errors.join(", ")}`
      );
      mainMessages.push({
        type: errorMessage.type,
        priority: 1,
        attachments: errorMessage.attachments
      });
      return mainMessages;
    }

    // Update the shipment through agent-context shipment service adapter
    // This ensures proper Candata updates and email notifications
    await this.shipmentServicesAdapter.editShipment(context.shipment.id, updateData);

    this.logger.log(
      `Successfully updated shipment ${context.shipment.id} with: ${Object.keys(updateData).join(", ")}`
    );

    // Build success message
    const successMessage = this.buildUpdateSuccessMessage(context, updateData);
    mainMessages.push({
      type: successMessage.type,
      priority: 1,
      attachments: successMessage.attachments
    });

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContext): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // Update shipment typically doesn't need additional validation display
    const options = {
      showValidationIssues: false,
      showDocumentStatus: false,
      showAdditionalValidation: false,
      validationIssues: null,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[UPDATE_SHIPMENT] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Build update success message using template-based approach
   */
  private buildUpdateSuccessMessage(
    context: ShipmentContext,
    updateData: Record<string, any>
  ): {
    type: string;
    attachments: any;
  } {
    return {
      type: "shipment-update-messages",
      attachments: {
        acknowledgment: "The shipment information has been updated successfully.",
        shipmentId: context.shipment.id,
        updatedFields: Object.keys(updateData),
        updateData: updateData,
        directlyAsked: { shipment_update: true }
      }
    };
  }

  /**
   * Build update error message using template-based approach
   */
  private buildUpdateErrorMessage(reason: string): {
    type: string;
    attachments: any;
  } {
    return {
      type: "shipment-update-error-messages",
      attachments: {
        reason: reason,
        directlyAsked: { shipment_update: true }
      }
    };
  }

  /**
   * Create error fragments for early returns
   */
  private createErrorFragments(reason: string): ResponseFragment[] {
    return [
      {
        template: "system-unavailable",
        priority: 1,
        fragmentContext: {
          reason: reason
        }
      }
    ];
  }
}
