import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContextWithServices } from "../../agent-context";
import { EmailService } from "../../email/services/email.service";

/**
 * <PERSON><PERSON> requests to hold or cancel shipment processing.
 * Sends backoffice alerts and provides acknowledgment to users.
 */
@Injectable()
export class RequestHoldShipmentHandler extends BaseIntentHandler {
  constructor(private readonly emailService: EmailService) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "REQUEST_HOLD_SHIPMENT" as const,
    description: "User is requesting to hold or cancel shipment processing",
    examples: [
      "Please hold processing until approval",
      "Do not submit entry until CAD approval",
      "Hold the shipment for verification",
      "Please cancel entry",
      "Cancel the shipment"
    ],
    keywords: ["hold", "cancel", "wait", "approval", "verification", "before", "until", "pause", "stop"]
  };

  async handle(
    validatedIntent: ValidatedIntent,
    context: ShipmentContextWithServices
  ): Promise<ResponseFragment[]> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling REQUEST_HOLD_SHIPMENT for shipment ${context.shipment?.id || "N/A"} with ${instructions.length} requests`
    );

    if (instructions.length === 0) {
      this.logger.error("Cannot process hold shipment request: no instructions provided");
      throw new Error("No instructions provided for hold shipment request");
    }

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(validatedIntent, context);
      this.logger.debug(`[REQUEST_HOLD_SHIPMENT] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(
        `[REQUEST_HOLD_SHIPMENT] Consolidated options: ${JSON.stringify(consolidatedOptions)}`
      );

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[REQUEST_HOLD_SHIPMENT] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment?.id || "N/A"}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to process hold shipment request for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Build main messages array for consolidated templates
   * MIGRATION: Replaces hold shipment fragment logic
   */
  private async buildMainMessages(
    validatedIntent: ValidatedIntent,
    context: ShipmentContextWithServices
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];
    const instructions = this.extractInstructions(validatedIntent);

    // Send backoffice alert for hold shipment request
    const alertData = await this.sendBackofficeAlert(
      "Hold/Cancel Shipment Request",
      context.shipment?.id || 0,
      instructions,
      context._services.emailService,
      context.organization.id
    );

    // Build hold shipment message
    const holdShipmentMessage = this.buildHoldShipmentMessage(context, instructions, alertData);
    mainMessages.push({
      type: holdShipmentMessage.type,
      priority: 1,
      attachments: holdShipmentMessage.attachments
    });

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContextWithServices): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // Hold shipment requests typically include shipment details (priority 2)
    const options = {
      showValidationIssues: false,
      showDocumentStatus: true,
      showAdditionalValidation: false,
      validationIssues: null,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[REQUEST_HOLD_SHIPMENT] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Build hold shipment message using template-based approach
   */
  private buildHoldShipmentMessage(
    context: ShipmentContextWithServices,
    instructions: string[],
    alertData: any
  ): {
    type: string;
    attachments: any;
  } {
    return {
      type: "hold-shipment-messages",
      attachments: {
        acknowledgment:
          "We've received your request to cancel/hold the entry. Our team has been notified and will take the necessary action.",
        instructions: instructions,
        backofficeAlerts: alertData.backofficeAlerts,
        directlyAsked: { hold_shipment: true }
      }
    };
  }
}
