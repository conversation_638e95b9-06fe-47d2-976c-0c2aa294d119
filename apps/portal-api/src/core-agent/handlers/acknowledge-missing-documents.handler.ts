import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";

@Injectable()
export class AcknowledgeMissingDocumentsHandler extends BaseIntentHandler {
  readonly classificationMeta: IntentClassificationMeta = {
    intent: "ACKNOWLEDGE_MISSING_DOCUMENTS" as const,
    description: "Proactive notification about missing documents",
    examples: [
      "What documents do you need?",
      "What's missing for my shipment?",
      "Do you have everything you need?",
      "What documents are required?",
      "Is anything missing?"
    ],
    keywords: ["missing", "need", "required", "what documents", "do you have", "anything missing"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    this.logger.log(`Handling ACKNOWLEDGE_MISSING_DOCUMENTS for shipment ${context.shipment?.id || "N/A"}`);

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(validatedIntent, context);
      this.logger.debug(`[ACKNOWLEDGE_MISSING_DOCUMENTS] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(
        `[ACKNOWLEDGE_MISSING_DOCUMENTS] Consolidated options: ${JSON.stringify(consolidatedOptions)}`
      );

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[ACKNOWLEDGE_MISSING_DOCUMENTS] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment?.id || "N/A"}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to process missing documents acknowledgment for shipment ${context.shipment?.id || "N/A"}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Build main messages array for consolidated templates
   * MIGRATION: Replaces missing documents acknowledgment fragment logic
   */
  private async buildMainMessages(
    validatedIntent: ValidatedIntent,
    context: ShipmentContext
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];

    // Build missing documents acknowledgment message
    const acknowledgeMessage = this.buildAcknowledgeMissingMessage(context);
    mainMessages.push({
      type: acknowledgeMessage.type,
      priority: 1,
      attachments: acknowledgeMessage.attachments
    });

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContext): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // Missing documents acknowledgment shows document status (as per original logic)
    const options = {
      showValidationIssues: false,
      showDocumentStatus: true,
      showAdditionalValidation: false,
      validationIssues: null,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[ACKNOWLEDGE_MISSING_DOCUMENTS] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Build acknowledge missing documents message using template-based approach
   */
  private buildAcknowledgeMissingMessage(context: ShipmentContext): {
    type: string;
    attachments: any;
  } {
    return {
      type: "acknowledge-missing-documents-messages",
      attachments: {
        hasMissingDocs: true,
        shipment: context.shipment,
        shipmentIdentifiers: context.shipmentIdentifiers,
        smartTemplateContext: context.smartTemplateContext,
        directlyAsked: { missing_documents_acknowledgment: true }
      }
    };
  }
}
