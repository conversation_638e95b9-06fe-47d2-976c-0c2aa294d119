import { Injectable } from "@nestjs/common";
import { BaseIntentHandler } from "./base-intent-handler";
import { IntentClassificationMeta } from "../interfaces/intent-handler.interface";
import { ResponseFragment, ValidatedIntent } from "../types/response-fragment.types";
import { ShipmentContext } from "../../agent-context";
import { ComplianceValidationService } from "../../shipment/services/compliance-validation.service";
import { CustomStatusService } from "../../shipment/services/custom-status.service";
import { ImporterService } from "../../importer/importer.service";
import { RNSStatusChangeEmailSender } from "../../shipment/senders/rns-status-change-email.sender";
import { ShipmentContextService } from "../../agent-context/services/shipment-context.service";
import { DocumentProcessorService } from "../services/document-processor.service";
import { ShipmentServicesAdapter } from "../../agent-context/services/shipment-services.adapter";
import { Shipment, CommercialInvoice, OrganizationType } from "nest-modules";
import { DataSource } from "typeorm";

/**
 * Handles document processing requests and automatic entry submission.
 * This is the most complex handler that processes documents and triggers customs submission flows.
 */
@Injectable()
export class ProcessDocumentHandler extends BaseIntentHandler {
  constructor(
    private readonly complianceValidationService: ComplianceValidationService,
    private readonly customStatusService: CustomStatusService,
    private readonly importerService: ImporterService,
    private readonly rnsStatusChangeEmailSender: RNSStatusChangeEmailSender,
    private readonly shipmentContextService: ShipmentContextService,
    private readonly documentProcessor: DocumentProcessorService,
    private readonly businessRuleEvaluator: ShipmentServicesAdapter,
    private readonly dataSource: DataSource
  ) {
    super();
  }

  readonly classificationMeta: IntentClassificationMeta = {
    intent: "PROCESS_DOCUMENT" as const,
    description:
      "The user is sending us documents as attachments and requesting for them to be processed as part of a shipment",
    examples: [
      "Please process the attached documents",
      "Process these documents for customs",
      "Submit the entry with these documents",
      "Use the attached paperwork",
      "Process the commercial invoice attached"
    ],
    keywords: ["process", "attached", "documents", "paperwork", "submit", "entry", "customs"]
  };

  async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
    const instructions = this.extractInstructions(validatedIntent);

    this.logger.log(
      `Handling PROCESS_DOCUMENT for shipment ${context.shipment?.id || "N/A"} with ${instructions.length} instructions`
    );

    if (!context.shipment) {
      this.logger.error("Cannot process documents: no shipment found in context");
      throw new Error("No shipment found for document processing");
    }

    try {
      // Step 1: Build main messages array for consolidated templates
      const mainMessages = await this.buildMainMessages(validatedIntent, context);
      this.logger.debug(`[PROCESS_DOCUMENT] Built ${mainMessages.length} main messages`);

      // Step 2: Determine conditional fragment flags
      const consolidatedOptions = this.buildConsolidatedOptions(context);
      this.logger.debug(`[PROCESS_DOCUMENT] Consolidated options: ${JSON.stringify(consolidatedOptions)}`);

      // Step 3: Use consolidated fragment system
      const fragments = this.createConsolidatedFragments(mainMessages, context, consolidatedOptions);

      this.logger.log(
        `[PROCESS_DOCUMENT] Generated ${fragments.length} consolidated fragments for shipment ${context.shipment.id}`
      );

      return fragments;
    } catch (error) {
      this.logger.error(
        `Failed to process documents for shipment ${context.shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Handle post-document processing with customs flow.
   * This is ported from the development branch's handlePostDocumentProcessingWithCustomsFlow method.
   */
  private async handlePostDocumentProcessingWithCustomsFlow(
    shipment: Shipment,
    context: ShipmentContext
  ): Promise<{ cadDocument?: any } | undefined> {
    this.logger.log(
      `🔄 SUBMISSION FLOW START: Handling post-document processing for shipment ${shipment.id} (Org: ${context.organization.id}) using existing customs flow`
    );

    try {
      const isDemoShipment = this.complianceValidationService.isDemoShipment(shipment);
      this.logger.log(
        `📊 SUBMISSION FLOW: Shipment ${shipment.id} analysis: Status=${shipment.customsStatus}, Mode=${shipment.modeOfTransport}, isDemoShipment=${isDemoShipment}`
      );

      // Guard: Check if shipment is already submitted - if so, skip processing
      if (this.complianceValidationService.isShipmentSubmitted(shipment)) {
        this.logger.warn(
          `🛑 SUBMISSION FLOW STOPPED: Shipment ${shipment.id} is already submitted (status: ${shipment.customsStatus}), skipping post-document processing. ${isDemoShipment ? "[DEMO ORG]" : ""}`
        );
        return;
      }

      this.logger.log(
        `✅ SUBMISSION FLOW: Shipment ${shipment.id} is not yet submitted, proceeding with validation. ${isDemoShipment ? "[DEMO ORG - Will skip filings validation]" : ""}`
      );

      // Get compliance validation using the existing service
      this.logger.log(`🔍 SUBMISSION FLOW: Getting compliance validation for shipment ${shipment.id}...`);

      const shipmentCompliances = await this.complianceValidationService.getShipmentCompliances([shipment]);

      if (shipmentCompliances.length === 0) {
        this.logger.error(
          `❌ SUBMISSION FLOW FAILED: No compliance data found for shipment ${shipment.id}. ${isDemoShipment ? "[DEMO ORG]" : ""}`
        );
        return;
      }

      this.logger.log(
        `✅ SUBMISSION FLOW: Found ${shipmentCompliances.length} compliance record(s) for shipment ${shipment.id}`
      );

      const validationResults = this.complianceValidationService.validateShipmentCompliances(
        shipmentCompliances,
        this.complianceValidationService.isDemoShipment(shipment)
      );

      if (validationResults.length === 0) {
        this.logger.error(
          `❌ SUBMISSION FLOW FAILED: No validation results for shipment ${shipment.id}. ${isDemoShipment ? "[DEMO ORG]" : ""}`
        );
        return;
      }

      const validationResult = validationResults[0];

      this.logger.log(
        `📋 SUBMISSION FLOW: Validation results for shipment ${shipment.id}: ` +
          `noCommercialInvoice=${validationResult.noCommercialInvoice}, ` +
          `missingFields=${validationResult.missingFields?.length || 0}, ` +
          `nonCompliantInvoices=${validationResult.nonCompliantInvoices?.length || 0}, ` +
          `${isDemoShipment ? "[DEMO ORG - Filings validation skipped]" : ""}`
      );

      // Use the existing processShipmentForCustomsStatus method from CustomsStatusService
      this.logger.log(
        `🎯 SUBMISSION FLOW: Calling processShipmentForCustomsStatus for shipment ${shipment.id}...`
      );

      // Create a query runner for the submission process
      const queryRunner = this.dataSource.createQueryRunner();
      await queryRunner.connect();
      await queryRunner.startTransaction();

      try {
        const result = await this.customStatusService.processShipmentForCustomsStatus(
          shipment,
          validationResult,
          queryRunner
        );

        // CRITICAL: Update shipment status if needed (this was missing!)
        if (result.shipmentStatusUpdate) {
          const shipmentRepository = queryRunner.manager.getRepository(Shipment);
          await shipmentRepository.update(
            { id: result.shipmentStatusUpdate.shipmentId },
            { customsStatus: result.shipmentStatusUpdate.newStatus }
          );
          // update the in-memory shipment status
          shipment.customsStatus = result.shipmentStatusUpdate.newStatus;
          this.logger.log(
            `📝 SUBMISSION FLOW: Updated shipment ${shipment.id} status from ${shipment.customsStatus} to ${result.shipmentStatusUpdate.newStatus}. ${isDemoShipment ? "[DEMO ORG]" : ""}`
          );
        }

        await queryRunner.commitTransaction();

        // Log the results for debugging with detailed context
        if (result.liveShipmentId) {
          this.logger.log(
            `🎉 SUBMISSION FLOW SUCCESS: Successfully submitted shipment ${shipment.id} to Candata! ${isDemoShipment ? "[DEMO ORG]" : ""}`
          );
        } else if (result.liveEntryUploadFailedShipment) {
          this.logger.error(
            `💥 SUBMISSION FLOW FAILED: Failed to submit shipment ${shipment.id} to Candata: ${result.liveEntryUploadFailedShipment.failedReason}. ${isDemoShipment ? "[DEMO ORG]" : ""}`
          );
        } else if (result.customsStatusCheckErrorShipment) {
          this.logger.warn(
            `⏰ SUBMISSION FLOW BLOCKED: Timing/validation error for shipment ${shipment.id}: ${result.customsStatusCheckErrorShipment.errorMessage}. ${isDemoShipment ? "[DEMO ORG - Consider bypassing timing restrictions]" : ""}`
          );
        } else if (result.shipmentStatusUpdate) {
          this.logger.log(
            `📋 SUBMISSION FLOW: Updated shipment ${shipment.id} status, not ready for submission yet. New status: ${result.shipmentStatusUpdate.newStatus}. ${isDemoShipment ? "[DEMO ORG]" : ""}`
          );
        } else {
          this.logger.warn(
            `❓ SUBMISSION FLOW UNCLEAR: Unclear result for shipment ${shipment.id}. No submission, status update, or clear error. ${isDemoShipment ? "[DEMO ORG]" : ""}`
          );
        }

        // For DEMO organizations, automatically generate CAD document after submission
        if (isDemoShipment) {
          this.logger.log(
            `🎯 DEMO CAD GENERATION: Attempting to generate CAD document for demo shipment ${shipment.id}`
          );
          const cadData = await this.handleDemoOrganizationCADGeneration(shipment, context, queryRunner);
          return cadData;
        }
      } catch (transactionError) {
        await queryRunner.rollbackTransaction();
        this.logger.error(
          `💥 SUBMISSION FLOW TRANSACTION ERROR: Transaction failed for shipment ${shipment.id}: ${transactionError.message}`,
          transactionError.stack
        );
        throw transactionError;
      } finally {
        await queryRunner.release();
      }
    } catch (error) {
      this.logger.error(
        `💥 SUBMISSION FLOW EXCEPTION: Error in handlePostDocumentProcessingWithCustomsFlow for shipment ${shipment.id}: ${error.message}`,
        error.stack
      );
      throw error;
    }
  }

  /**
   * Handle automatic CAD document generation for demo organizations.
   * This mirrors the handleDemoOrganizationCADGeneration logic from the development branch.
   */
  private async handleDemoOrganizationCADGeneration(
    shipment: Shipment,
    context: ShipmentContext,
    queryRunner: any
  ): Promise<{ cadDocument?: any; acknowledgment?: string }> {
    // Double-check that this is a demo organization
    if (context.organization.organizationType !== OrganizationType.DEMO) {
      this.logger.warn(
        `🚫 DEMO CAD GENERATION: Shipment ${shipment.id} is not from a demo organization (type: ${context.organization.organizationType}), skipping CAD generation`
      );
      return {};
    }

    this.logger.log(
      `🎯 DEMO CAD GENERATION: Starting CAD generation for demo shipment ${shipment.id} (Org: ${context.organization.id})`
    );

    try {
      // Get commercial invoices for the shipment
      const commercialInvoices = await queryRunner.manager.getRepository(CommercialInvoice).find({
        where: { shipment: { id: shipment.id } },
        relations: {
          vendor: true,
          purchaser: true,
          countryOfExport: true,
          stateOfExport: true,
          commercialInvoiceLines: {
            origin: true,
            originState: true,
            tt: true,
            tariffCode: true
          }
        }
      });

      if (commercialInvoices.length === 0) {
        this.logger.warn(
          `🚫 DEMO CAD GENERATION: No commercial invoices found for shipment ${shipment.id}. Deferring CAD generation until invoice is received.`
        );

        // Provide a user-facing message so the email template can show a clear explanation
        return {
          acknowledgment: "We will generate a CAD document once a commercial invoice is properly processed."
        };
      }

      this.logger.log(
        `✅ DEMO CAD GENERATION: Found ${commercialInvoices.length} commercial invoices for shipment ${shipment.id}`
      );

      // Get organization importer
      const importersResponse = await this.importerService.getImporters({
        organizationId: context.organization.id,
        limit: 1
      });
      const organizationImporter =
        importersResponse.importers.length > 0 ? importersResponse.importers[0] : null;

      if (!organizationImporter) {
        this.logger.warn(
          `⚠️ DEMO CAD GENERATION: No importer found for organization ${context.organization.id}, proceeding with null importer`
        );
      }

      // Generate CAD attachment using the existing service
      const rawCadAttachment = await this.rnsStatusChangeEmailSender.createCADAttachment(
        shipment,
        commercialInvoices,
        organizationImporter
      );

      // Clean the base64 string to remove any whitespace or formatting characters
      const cleanedCadAttachment = {
        ...rawCadAttachment,
        b64Data: rawCadAttachment.b64Data.replace(/[\s\r\n\t]/g, "")
      };

      // Validate the CAD attachment
      if (this.documentProcessor.validateCADAttachment(cleanedCadAttachment)) {
        this.logger.log(
          `🎉 DEMO CAD GENERATION: Successfully generated CAD document for demo shipment ${shipment.id} (${cleanedCadAttachment.fileName})`
        );
        return { cadDocument: cleanedCadAttachment };
      } else {
        this.logger.error(
          `❌ DEMO CAD GENERATION: CAD attachment validation failed for shipment ${shipment.id}`
        );
        return {};
      }
    } catch (error) {
      this.logger.error(
        `💥 DEMO CAD GENERATION: Error generating CAD document for shipment ${shipment.id}: ${error.message}`,
        error.stack
      );
      // Don't throw the error - we don't want CAD generation failure to break the entire flow
      return {};
    }
  }

  /**
   * Build main messages array for consolidated templates
   * MIGRATION: Replaces document processing fragment logic
   */
  private async buildMainMessages(
    validatedIntent: ValidatedIntent,
    context: ShipmentContext
  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
    const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];

    // Handle post-document processing with customs flow
    const cadData = await this.handlePostDocumentProcessingWithCustomsFlow(context.shipment, context);

    // Process document attachments and prepare detailed information
    // If no attachments in intent, fetch from database
    const processedDocuments =
      validatedIntent.attachments && validatedIntent.attachments.length > 0
        ? await this.documentProcessor.processDocumentAttachments(validatedIntent)
        : await this.documentProcessor.fetchProcessedDocumentsFromDatabase(context);

    const hasAllRequiredDocuments = this.businessRuleEvaluator.determineDocumentCompleteness(
      context.shipment
    );

    // Debug logging to identify context issues
    this.logger.debug(
      `🔍 FRAGMENT CONTEXT DEBUG for shipment ${context.shipment.id}: ` +
        `processedDocuments=${processedDocuments.length}, ` +
        `hasAllRequiredDocuments=${hasAllRequiredDocuments}, ` +
        `shipment.customsStatus=${context.shipment.customsStatus}, ` +
        `attachments=${validatedIntent.attachments?.length || 0}`
    );

    if (processedDocuments.length > 0) {
      this.logger.debug(
        `📄 PROCESSED DOCUMENTS: ${processedDocuments.map((d) => `${d.name} [${d.status}]`).join(", ")}`
      );
    }

    // Build document processing message
    const documentProcessingMessage = this.buildDocumentProcessingMessage(
      processedDocuments,
      hasAllRequiredDocuments,
      cadData
    );
    mainMessages.push({
      type: documentProcessingMessage.type,
      priority: 1,
      attachments: documentProcessingMessage.attachments
    });

    // If CAD document was generated, add CAD message as well
    if (cadData && cadData.cadDocument) {
      const cadMessage = this.buildCadMessage(context, cadData.cadDocument);
      if (cadMessage) {
        mainMessages.push({
          type: cadMessage.type, // Use the type from buildCadMessage()
          priority: 2,
          attachments: cadMessage.attachments
        });
      }
    }

    return mainMessages;
  }

  /**
   * Build consolidated options for conditional fragment control
   * MIGRATION: Replaces individual fragment addition logic
   */
  private buildConsolidatedOptions(context: ShipmentContext): {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: any;
    additionalValidation?: { issues: string[] };
  } {
    // For document processing, show validation issues if the shipment has pending statuses
    const shouldShowValidationIssues = ["pending-commercial-invoice", "pending-confirmation"].includes(
      context.shipment.customsStatus
    );

    // Build validation issues if needed
    let validationIssues = null;
    if (shouldShowValidationIssues) {
      validationIssues = this.buildValidationIssues(context);
    }

    const options = {
      showValidationIssues: shouldShowValidationIssues && !!validationIssues,
      showDocumentStatus: shouldShowValidationIssues, // Show document status for pending statuses
      showAdditionalValidation: false, // Not used for document processing
      validationIssues,
      additionalValidation: undefined
    };

    this.logger.debug(
      `[PROCESS_DOCUMENT] Consolidated options - showValidationIssues: ${options.showValidationIssues}, showDocumentStatus: ${options.showDocumentStatus}`
    );

    return options;
  }

  /**
   * Build document processing message using template-based approach
   */
  private buildDocumentProcessingMessage(
    processedDocuments: any[],
    hasAllRequiredDocuments: boolean,
    cadData?: any
  ): {
    type: string;
    attachments: any;
  } {
    return {
      type: "document-processing-messages",
      attachments: {
        documents: processedDocuments,
        hasAllRequiredDocuments,
        acknowledgment: cadData?.acknowledgment // Use custom acknowledgment if provided
      }
    };
  }
}
