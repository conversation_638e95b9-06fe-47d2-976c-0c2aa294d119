author:	coderabbitai
association:	none
edited:	true
status:	none
--
<!-- This is an auto-generated comment: summarize by coderabbit.ai -->
<!-- walkthrough_start -->

## Walkthrough

This update introduces a consolidated template and response fragment system for shipment and customs intent handling in the portal API. Multiple handler classes are refactored to modularize message and fragment construction, centralizing logic for building main messages, conditional options, and error handling. New Nunjucks templates and types support this consolidation, and intent classification is enhanced with dynamic metadata and fallback intents.

## Changes

| File(s) | Change Summary |
|---------|---------------|
| `handlers/acknowledge-documents.handler.ts`, `handlers/acknowledge-missing-documents.handler.ts`, `handlers/documentation-coming.handler.ts`, `handlers/get-shipment-status.handler.ts`, `handlers/process-document.handler.ts`, `handlers/request-cad-document.handler.ts`, `handlers/request-hold-shipment.handler.ts`, `handlers/request-manual-processing.handler.ts`, `handlers/request-rns-proof.handler.ts`, `handlers/request-rush-processing.handler.ts`, `handlers/update-shipment.handler.ts` | Refactored handler classes to use consolidated fragment/message generation: added modular methods for building main messages, consolidated options, and error handling; replaced manual fragment construction with calls to a shared fragment creation method. |
| `handlers/base-intent-handler.ts` | Added protected methods for consolidated fragment creation, validation issue detection, and standardized message object generation for various scenarios. |
| `processors/handle-request-message.processor.ts` | Updated task intent to email intent mapping for document and shipment update intents. |
| `services/email-intent-analysis.service.ts` | Injected `IntentHandlerRegistry`, added fallback intent metadata, enhanced LLM prompt context, and unified document-related intent mapping. |
| `services/intent-handler-registry.service.ts` | Removed the `generateClassificationPrompt` method. |
| `services/shipment-response.service.ts` | Changed deduplication logic for response fragments, merging main messages for the consolidated template and keeping the first fragment for others. |
| `types/response-fragment.types.ts` | Introduced `ConsolidatedFragmentContext` interface for structured main messages, validation issues, and display flags in response fragments. |
| `templates/consolidated/*.njk`, `templates/consolidated/messages/*.njk` | Added new Nunjucks templates for consolidated messaging: main messages, document status, validation issues, status line, shipment identifiers, and various message types (e.g., CAD, rush, RNS, hold, manual processing, updates). |
| `llm/prompt-templates/classify-intent-only.njk` | Refactored prompt template to dynamically render intent types and metadata. |
| `testing/e2e-email-pipeline-nestjs.js` | Changed default attachment behavior; introduced explicit `--with-attachments` and `--no-attachments` flags. |
| `test-classification.js` | Added a new test script for validating email intent classification. |

## Sequence Diagram(s)

```mermaid
sequenceDiagram
    participant User
    participant API
    participant Handler
    participant TemplateEngine

    User->>API: Send intent request (e.g., document, status, hold)
    API->>Handler: Route to appropriate intent handler
    Handler->>Handler: buildMainMessages()
    Handler->>Handler: buildConsolidatedOptions()
    Handler->>Handler: createConsolidatedFragments(mainMessages, options)
    Handler->>TemplateEngine: Render consolidated templates with context
    TemplateEngine-->>Handler: Rendered fragments
    Handler-->>API: Return response fragments
    API-->>User: Respond with consolidated message(s)
```

## Suggested reviewers

- sophie-aistribute
- danny-aistribute

## Poem

> A rabbit hops in fields of code,  
> Where fragments once would overflowed.  
> Now all is neat, with templates new,  
> Messages clear, and logic true.  
> With every hop, the system grows—  
> Consolidated, swift, it shows!  
> 🐇✨

<!-- walkthrough_end -->
<!-- internal state start -->


<!-- DwQgtGAEAqAWCWBnSTIEMB26CuAXA9mAOYCmGJATmriQCaQDG+Ats2bgFyQAOFk+AIwBWJBrngA3EsgEBPRvlqU0AgfFwA6NPEgQAfACgjoCEYDEZyAAUASpETZWaCrKNwSPbABsvkCiQBHbGlcSHFcLzpIACIAMXgADwB6EmZtX3gMGizGLzRERHgAM3gGanh8DGReFm5QxAB3dQZYaMgG/PtsAWZ1Gno5MNgPbERKSABlWS8pRBoKAGEF9GRbSAxHAXGANgBOAA5+LFxhyAW8inw/Em58QoIXDRhTlsxSZEyGL2wlBSr8LzwWjlDBEIYeGjMbh5Gj2WRzVJhK7+Zj4KSQWjYaGlageNgFNDvdAYeglBKZMEnDyIfAUcSg/hFFBZdjIfwwqIEHiXBjSZC0fAMRys4n0IVzFjIL4kZyYXlPdxjSBY4E0ZDaZhIlBQy7oqmQWCYWiAhlG3L5QolMriSqMyCpdLM7K4GTyOb+NDME0UyD4xCEiGpaG49UkhQUfxiH362lKPj4JmIbiiYqlJ3sRi4oi0+DSBWnNZlLBba4SXMNKKZDEkXCO6jg7looFRACqNgAMlxYLhcNxEBwkkkiOpYN0NExmEkAIIASXd8AEeBISXOznwSW43i8ST2+w0Rn0xnAUDI9ATOAIxDIyn6ClY7C4vH4wlE4lmkEGTDjKjUmm0ujAQwTCgOBUFQTAL0IUhyCoW8JzYLIuCoBouicFwP3kL9lFUdQtB0Q8j1MAw0G4PsN1pWsvDAEj4CSRAKAYJImH8ajoNwJJDRJSIKEQJI0AYABrDB8AaSJaFIMABSFBCXQ0TjjUoDQXQ4AxojUgwLEgWcrxg3F6AcND5HPV5QWkNxTgAA3kyILN9GtYEUdpOn8Ip+IeTlkRuPJeXQewKUiexa3EBhriTSolSKKgiBk9oR185hvHEMAEW4RteQKIZ60XeAvFodU/hpQFVSiMZQnPfwwqqDxIsJGTECeGdQmElD8lkDAWkuYTRi8TDwtwChsDEfK0irP0A3VCM0HkMs0EgCzstygBZbQMEWvlxosgAadoEBaRhsAjdgeuZL4fmkXzClBAKpOFHJ+KEkSxOijMxtIWKTjCWRk1FdAe342A6vzMhtvUSBoJvc6mH+Iq9P4OoKiqSAijyIhkBOS5sCIWA5oW2gFnCgEgT0gB5eHwq2nbSmx2gkBUSJkAkNAYZtLAzRumK5moUYMSQYN5GRwl6sgFtLspYYlUyTcXRB0Iyh8ZALIYD0aHx6Gif6WIorq2yuRqTEfP1EoMCZ0LbiqpGtZFZwqFkIG7JORzUAaKhSMrVmwhcMBrT2gQvEFAStT9sEzX8dGRPtCNaXqowJixW46R9XhJFxe2HLy9pKA8NBaCUWguHm7ActoZbMjWgl3ls0ODsRiDramu1Xo8QQRCG97saTnNcHkM1qFrFo6oAbhxwvctVwr1boUmWcQSua2r/KyRKms7QF1G/n6gETTBGrnpyGmkzyWQh7NAui6nQTmqekgy/9UhbKVmU1QbZis4vx66F30JG7b+wEG4GKzRQxoAkUIqo0B214CQMs+BuY00jKEHeMUobukGizH+s1ITBlhMbNgTk2SpDRHQba/hgy8gGPIE4qBkGE2KrQbamCOSSQoJIMg6BSKXH+vuDSlgpxeHmOUcKWp9RKC+LKaedoSAJHjreWknhfZpnYOoXMiADyQDWg7fS8AiDG1wAdEY3BaH51au1A0RpIgAApGbMzoDOFkiFIAADUmYT1oLY5020gGSM4JMP+MlVbANwAASi4FYS4vQxjABsNIM2YxNa1XYAAbQALp6FslWURGULLnweqJD+JAAAigpbougABJmMoGkrAFkSJkWkUzai3BaL0UYi/Vi7AOLlJ4nxN+uTxIkEkkUwG1lFIulsuYoOaYTKkFJGE64uiKAYB9LNUWAVObBQtvEnIXJZrUOsTMzZoQ67yCaB9Sglw+DWQpIEgwUB1Fp3QDnOg+ck6M1hMYkKuMS6rXWu8SxzjaFuIfI4/5elAVZA8ZUAJXAJi+PYP4rxwTrBhKQCQYAU5JqyGAAAb3XuwAA/FwecoIh5d2TASwKzDiXcgqMwruXANjMC2BQY+f1+6snJZgeQABfPQqTmTmkydky+eTCnSVZGUriFT+XVNIrxOpVEaJ0QYkxWk/SAxZA6ZKrp91hV9IGWKrI9VhkUCUjPG5aj7KOWzrnZ5zDXkeFxmPGhJMyZVHMZ4kB0LYVZHhSAxFOLEAOQaE45mCM5wOGkOSgQ+AAQygwEPQNIlRXFImEFUYUaY2REwAmoNU4c5KMqEzEN6sEYZtjdmyAViS2VHDcERAHKMBHweTTFmRaQUs3JTipAEb+wUopMkyAXLB2VIFQrIV78+nJrqhKhSFAR0ytqRRepirmkqpYuq9ixreI6onRJdmrI5KdNNRZc1dyrWPLznNF5Kdcbjt6aQG+AZ3WQq8V6+A/84Uvr9VwHFpKSCEv6hSFlfcAasi4Jy4d/KMljp6VfKd4rOnzpqXKpdCrGlKpaaqtpGqt3dJyVffVxSjVHtGapdSNyiLIfInSZd6HV2tI3Zq2d27YN5LAOE0WhGhkkf7GR6I3CtIzh0hDfSjg0joWMpxd45kPBWXKbZNgGi8HXFcmIVV9AuQkO8lnPyUJVlBTTIgjMVcFnoN9IleAyUaCpV2S4jZn8wbXlggjNKfJ2gu2TPQKsGDPbe2xr7f2SNZFnNkZc0EDUmrh3ebATqMDEDHVxsNFadly7nSOZW+As1T5LRWo+iu20Gi7Wxsg/qqD8orNfvhvJvpu0+n3YalLt8PAnOxn+n6vd/qA2eMDFAoCayUF6OQKUlQW0IxNqvZARRZFGb3rzQ+QwMZY2HkXJ1eyp4IxngVor9gazIHwK65ADg9qdAsomho8Gsipq5jPIRA0s5hn3nTc6VbVQIzAOyWGE2gZKhGlgRuoYzwHfQP4HgFoPJzQfriVbLi4mfxu1ycGsEISnCNibCqMTqqW0NU8ds+AiDDgZB0dUF7fr2n+oFG4QW+BKEXPjpZYYQt8BIRRCkED/DQNgfAeB9mYqbkDWZxeGmgwcmUyiQhtA7bkBQte2EwwvDJj4IptOyAyBlD7N4FO+pv4lYGlGW0Zp9toKUPMQbAiMDEK8vxaMpwO58EyCaTHBzGDKxcxMhgXDzA8L4TeDbQjTgiIuGbvbTJJHSKiLIzc8iQqKPEGZU9lrNHaK5iDlUekjGIDaiFYZfy9lgu8cWgFdjcAQpZJ6nx76/FfqCSE5FESomVViVj3AyS+XpLyIK1jfTFq1dBBd0piHpVUflQ0ppyqGNsSY9xFjVW9UcYpFxg9xrj2QEACgEjApNREiiwOZ1clmBXKCFGbhyMVah2QTPZPOMyI7Nz/RnpiuKs/j0p61Tyr12pTu8j8I9i65Z+dIHPLieeXABeoKReJeUK5eH6PqVeiKoSLAKKaKGK2KuKWQ5KRKRAJKX0JAaBgGVKtu6gsg9KmwlAwGnW7K4Gjag6vKI60Gc0d6V83eBQFIfeiAM63ESGsq1GlEI+GGa6aqE+uGO696/Sc+oIC+2OS+pGtyCezar+FkMuDq3+0OtC624Uz6pe3iMKFen6Gh/qv8IkIBLMtakaXA0a5a8a+h52gy7AV2ui9aphmacaOaIkeao2haXghhpaDh5hQ8L2Zuxh9hxITa1qBaxsHh7aXhkAXaBQdaAGlKRAA6Q6XKNB7eMGM+D6PeRALBbBUqVYC6KGNGaGo+mG66AhnS0+uqEkohRA4hskkhZq0hz+F6tqycsIt6neGRTBoIeWJA6hEBWhUBuAvq1eURn0yYcRQGv0IGdUFB3KKRFodBHR18mR2RA+eRQ+qGPB9GWGjGghSx7GmRtRxGWqx6fGB4lGnBw+K6Y+OxZRWqvEAg+Q/SmQzoYA9RKkak/Gmk2k1+t4Bk4mRkTIUyce7gPMRQTILxlw+saW6wJAKEtmtCYQwuKcGeCImoLWVY+oFkAAQk8XnjkXOqOhhM2j6AlHwu+gFDUDQGIFEErooJNkUmMGeFgL8T6JzCSM4DTAAF5RDo7hSO5w5U6/zaE5AvEHrdaQACQkDdz5poKoCKzKwkDKF6Sw7axbZUwFSlatzLLLzlTRL8mX4NajB74ImwweqhAnD1ifDfBKDIC/aNbjTbR+FykxHSDbT1b1BpqIDbQ9yylja+DOkuZfbPCoB0mA6to+DdwMC8h1DVAsQLShCBm2jdp1r8B8CJbgiahikLJMzHQzQNhS7La5SeE1quk3ZhlcLZa0AlkYABG2SYBMyyA8loynCBoimyxV5ajG6vg1ZdFggenIDmIkAaBEAaAQr3gMSZYZAYASD4ClAkCBI/TVFIy5i5SDnDmjkFYkBaLdjbTSIKBKAeLODZjryXC+AMpMrbTEwADi+SK5W8++dhgSFu8yiypoFKqCeiZ4L4YgaZ6wW4KATIwkKAZZ9o5IcwXCrhoRJscuCuqc9JjmuksISYKYVoDpb0zcr4DJfA8EtoiAvIxszCdw+cuMthowPRs8r55WXpfaDIjxTJRwwpgx+0EozAh2NFZoiA4mFpyJsI5pQ8VZCw2cFFzuj8MJCwU4d5Hp1wQQIQ6FTcTOc8CyyAGwPgQpvINGVYayow0gAluMNgowsAIlJmiM25VIfAs0A0/O38bWsis0rkPgjxgkj53MniOQU2fAyYJIbJXpulP0VZNgVQIlkOT8EENgAAchMI2HqbJXMPJT/Ibv6VMWQdjjJj9gnsNj4K+CwsdA4KRBRL5FLiXoUAiHdOwvgOTlyLjD6HyebEfsgC1tSp3PADyULnpiGD9OadRB0CDm5cXvaBgJxAwNbg6k8emDkMagAORSgkQqA5RKLnQtYwKHLe7xFgVID0hghhl7Z8CFBJ7zJ5hGCe5aSrVB5+4eAB5iK+7nih4UTh6eXdCAjR5ZALUqLmquFRBUmvi0kyEKliXKkaxN6IDmK/Y9G9roo2zYp9U4HxGYFkoTF4FEW0qEH/mMokHJVsqGoNqyA8rgGvqQGV4aHbSJXhSdqWE1kBFlpZoWFnZ95kWBFmHU3OENBQURk1lU1OGVoRGlk9rkoA10Cqk6EBIJLRBJm1llnRBJLHx+nuHs0/ogU9oI0JFJKDqDqIr14Y6C1ZDJLzGZK4ljD4lrFVIbGFFbE3GlHtK4b0XPFF5vE8YnrvUk5fU0n0BhlFnVnc3i09p9H40DGE0BKIr820Ba1DFV4i1i0BGS2QAAA+/5PgutCs+tJAhtWqHBi6pt1xJR/Blt5RSQ1tYAYpWQdtJxpGjtucjY1Jt4btVZ9NPRPtZeftQtCKStCdc0SdKds6adBR3BmdfB2Gm6ud+dhduAxds6pxZdn1lwldP1Smglwlf+9dmh3qodRNmYtAfe2Nehf6StpBmNLo2NqtsdqlXgrdOJeJReBJXdXBtGxRfduxg9TxBdtt9RDtUAH19AztVdv1+lhldd5pb6gxwxW9WBSth9oxfVoDyRUGqRbd59zol9g+lxmxvd4+Od9xedj9w9o93E49b9TtU931rt393+gViAf9VeAD/tXixCVQ+S1AaAm98t29tFGBGNoGWNsxkGbeCxZ9BtF9Rtc0JtPddG5t2dOGD9YwT9rxL9ZxFGYARgQjN9vBqD4j6DHpZuXs8B4W7xZx3xQmvxJUYmzggJ6+bwIJlkwyCmMheRfeZu+Mg2RACDROKmbk6mWoWm/EF1XOPZR+mpuuaCjVZ+asiJvjqJkIDUVQNA2cDcmA2AuZ8gfOCADImQNMZYmI42QNMs6w4ccsXgCsnyv+qW8OVw0WsW3U8gBjxIwONsMTo0f+z4LcLo1wkCYwL1DI+oL8KBmg3W5uvWmY8s7tQdqhVQOsVwesg0OmpUK8KMw2WQZ5D5UMbhYRhpoQ1pxplQ32WcIO3AYOGmVwf1UO5+MOQNoziFEMDYqOvgtVEUQNdsbtTsHmbsvk/UsgXs1APsfszlXIQcP0ocMW4cjO0cBgoJkCHO7FB+KziAK+tcHRMUvjjV+BXckAAAjO1kxTFMbukJNk3j/Ii/IAAEwr4Vgg5i5SAzLb5wI9k66oIIwRMIjZzbRVlfJg2UXzxVPpZAIrR74VbxWYW/mNVtYWTRDqMsyaMOPsZ/6IDRAUx4sos+lhgdZ70fDtQ2n07oCwsZgBJourzMg0zWgjXKhjDU7eNiDHT5BSn0AqDLUYjWFZA34TiP4GCCVHMqEHasvKW+SC5wziJ8urPeX6vtOGigK2uenXb6HeADDUhBpYDmKlYLlOme0K2pm+lLMmxi3A5eP+i+xRDmIOVjDPm/MkAcj0BgIrlFvpzOAeDH1/nYAkgkBGx0BPD5IkC06QBBz4L7lUhKgk1YBWJ1qQWwkoSwXjDV24y2Msz2MUgiWq4kQOAcgtkeAhUubnjCIhv2taNbX1N9XtasrsOyRGDExSBUA+D0IvAb41ZEBI7Yvb6zSGgUC0BYT7IOaAhxXbK+iKAa4UDFXOq3i+Osn67lXk6NWh5PWgx35hbBwPY1jpBRA06Yxtt44E6jlHUCa8L8LiJcjCKiCB4+sh5SJ3VngPVR4DXhDKKqJnqJ46J6LKgGJp6CMZ4mLZ7Ok2JF7AHtosfuK4oN3L1AM17wF176lVQh0t6n3jsIyTuggIPrFIMZ0iNZ392T6UC8QitvYOvaP22r6mOmTkuaiUumsJOGU+UQv1VajGkMimm/s4v/tYDAeRwXJmKP6NH3Iv6XryHv5vIMcfLf7MuSsAGF7Ohse55gFcdL3tm8dIr8eooQ1TTIHQ071jHYHxd4tEFo3MpsN1QH08qt5YC0EWRieVASeOMCP5HX1FHKO3FoPMZJAqeVBius4yNOfno2pv6tGKErYusurTyL2UNN3fqjFnYU1lkc0Vq00hv03Dc025oy1hFy0fiOEVoR1DezHS2pvhGhqVBk0pnSBK2JFcM5cwN5drsTsbtSfG0yfCO30qMD1qNHeqcbuHol0NEWpNHNduetdf5Fz5cYCFfkMaE9fQG6FMMgMsO717u9oQZQPcOZJfeFeneCPndKPbEW2qNVc1cYB1fqePcnqfHnHyPEQI9ldI9iPXdVekAj1tmDFWbXYPdj3KS6M8L6NOawz/HGN2jAlvWgkWTXk1iN2XZekIO0HOMuSuP+B7MXtI4Wzb5kniDQiZsPywi+MhEYclMFQ/uci8XUjwiQiUyUkVSUBlimhqWSIbU+hFC1t65hEEF3PJZyaSpWNKbOOFB6aphRCDCkt1YmsII4vpZpCLKbgwguZmiZD0sDDf576IstVRDpbnj2nfy+sMyZaFVwnu0+dFP29pz5ihkyEePpQNigsVDczVI5wTDJjDVWgACKdaLMIdN2gCowBAbFSQy56gqQ2LTubuxJrJ5nL6OQ6B2FkAAAotAFOPQlQFUG5C5heZQBblmkqNpd6UxY0gyPP/K5a38Kt2c8ey5XaX/ttDKHtIE0wdopPTSq9QO4WaO0oR1/0MMzds45CR+2QlqDO+riLos9Bb4BNj9KW0OSOWOVzetywBbcF+0lefouXz5xZjowychDth2YS9Fe03IWKCUuaQsM2olWGIMFmiFkDmKsa/gLRObwV6A5iYSPUGjb8phExQIoIuTM5iw8QyWf7J1TwHhkNsXCXHHTkJydAyAQ1KII1WQQbUMwFkBJNeSH4AB9CYCUhnBWBFoA/cKtADEHD9oALYCYEklsiQJF4/fLYD2HGD9RPGc1QEF3DtjQZUw+rW0B3x7hqVMAjQcYNZ0IH4J7S5vDfHQm5DSB9ePoY3nMB9BbBDQ0CE1BKV6CXt12wTDqr4wg5OgVeqIFPIshKBR9AOLQC3KQlJIWZZeKzTwIGkWpxRlk/kRdkwJSFLs8K1mLhN8VOrK8Gwl1ZzIIhur4c6Q91ORE9RI6vVyOMhPalRxTy0d+g6eTPPfgUh+dQCAXYFEF047/0CavXEYnAXCSooNa/JYTiklPrc9cAvPeYfz2K6KNCeojBTrhjJ7JRl6VPOwjTxwajJIA5ibMssw77AknBwFZocnkXYb5rkjXS1s0Ra72oVgnQgpqXF85GCrQQeaFDWGADoE9AeNbjmFyrzbRYq08HbkklgK14ouSBHFHFxB4JcYakxZLqjSZRUFsuRJLnjz2Xr004eJXK4nJzvp3FSeNYLYe2R2GjA9hIyJ7hR1kKucFCH3UeEwNv7upUixgr4ZMB+F/CARoXQBjAXloDdPalNbwkzUsJ00vSE3ZmqzX9KzdGanNRbrzWW4kk2antTbmWXBGq1Ie+3HhnMIWE4jlhBPM2vJ3vroNNhFPGSOSOOK08zUOPORgowNEoMKuKPKfBuB5B8hailIk1HTxx56NhMSOUTIZDZ4b4OelkOAulEQB94BeMDIXvWxF7g4c+yODwBAO5ggdSgoMXxtSwt4IdhwIUTEhY3kyECzMFnTfFZyZ5oIakHCFoHbBzAE4TYWEHmPAmOg1AwxsHENulxFDTYawLQTkMME1Bb5NQwiehtbT359tYY0lCcLL2yB8htoQcaZNWFrA5RYOLbeDpkA8ppAWYq/X0LE3iapDhgrtJIQFGuYCk6oP8FCmXzTAMIOqZoCPodWBanBCy8APTKkHYA34lAkQIgB1S7YeBrmC4earHjRhXB0YJAKtsn3pHbUSK3nQpk1k2yDMmRbrDcVWT7yhi+QU7P/BZDti28FI6fRyM1H6Z5MU+kEjaKfk87/Muo8WCpqWPuzVN64MfegfU3j7NM9ebTfsq2KbHITTQYYCSneX+wgwmQNSJ6k9gizggcuuZfJlfyCE383WXZfrBQEGzVQZmp5TeD6Hf4RkUh+8PmB+CeLMl0WGYcUA33BZ2Efo6bYAU8HiBhEeoWTXJgrBCpKkmBNfWyAK3Fh4hJWP0Htv+MbDQkLmmQNHIJxuYHIJoNsLhJz1eHfI0+BY44duIVyriF2PAO4CPWkqsS+yLFPSUjD9gNA9yrogoOdA7G4Aux/IENgvygRMw4mT8ccZEEnEFBpxeOfkEuLBArj8AG4jMU0zj4/kmmHlG1galCAJTRYP0Tie1KIzoTHUsE6eFhPoAv852HVDvlyBETNgdo9kbQVcDOwADq0QA0CmzFbHz9UB1tLSWaIzDz8Bp3+RCRlNFgiU3a1k4aHRJak/xpKpbA3K6hNhCEnoMkQKQgHwSqYHgjAdgMe0j4A50AAgd0G5Db4OZGpCMeId5FJJbjP+OLRJukI+hBNx4ITHFmE1SAgwdQTYBkKiExAXACCS5FaPOONhqB9BtsCUoQmPa+AgEZ5FKVFgZx2cuhD5FEMlj4GlV+qjVTFguPoAzjE4evCgGS0KFe50OvuTDv7mw5XUKheHMPIR1qEKIXqf41RO/WpRPDL+RcVPlBJ6H9AgC/QwAsFyGELDwuYwhAtF0xSwie+uAREVSj/SmzWGyIqfml0VZg9Mu1BaBjwyQkFAIx+o9Ohd3K7I8Sezorqe6Ia5aR8G73RWYyPEmTwDs3XYYQDwDr8ig0g3BUXNx8Kiixu4o4UZzTOxSjZaKo1OQt0FFLcgiK3D/rN2iKK0WGu3TURiOdnhiQ2uIlYYaMJGVcfZR0v2fbQnof13OTk2emOxDaVyUJqWcxF1LoAsFZiySbaIaEQC8IvAUSIIN43Xr5Ts5vTMoOvXoaMNRizDdAqDxmJBE9uFco6a7NTqIN3ZiPNYcaKq6+yPSHo04jaIgB49a5Dor2Ypy6T+BQRbzWgM3Me4fF1IPogxv6IBKBizGwY2TFPKr6cS95ndIktGLeluNNMluA2DbnZwF9+QnvXIX1H8YuZAmqvC/KEy16pA7Yw9asPhWYSuplMoiZhNELF6njigFCU4L1OkqdB9QCwevpKC0jRkYETEyAH3iFL6gdpWQdCZYwLE4SvEVAVuMHy1LiI+xDYZjl5iLw/R22ZmCIRdWg5sznBZIQSUexd6HZl6zg1pj5DNBUgsArEugC2UWytYYsQEzcVpWsy9pqq6M2iUU3dLSTBsSknIa5Iaku5u+ocp9l1hkzOCwWWY88UG3rGvhjo383yL1MPEpCe42kveIorwnwtnIBCHmZAFsQh8fSg7eWSnGDk/43hoU0ppUEgHkSkKaWVmCfhol1NUsDTLCiDBVY/AfQNC1sTYOKDoBGYOUJ7AVjijgcHOgbesLItcg5Q9EMgPAOvEyCpkAhvIM3pGSeBTgSBwwPgIWVAnEN2uHi2/vfFqBLhJs8k8mYpPcUb9fGakw+NtCOzYxOghWOaXwC5CLSjJoFWRKAJopbTGK3C2WIwrYrb9QZVuBkLbl1aSAgQcTSGU7iV6u48cpQZ6bJj4Vu0cJJlaKSZ2skwCcBNkjxXZMNYGtY+zkwBM4oOw45EOPoZxlwLlDg4dBsCqtsQXjBMhkVRTBkf1Q6Zorp4G4/UNbLtAhL6qVYo9rmQUmf9UpwpIoJZyl4QyIlmUxlMdDfYYzP237C/OWIqoH84o0oStrtRuAiysAxkSoBpRrhhgHxNQMlhHHOS0y6sB0WpZJT6kxRrOvMk6vzMESCyFFJCs6pUPFl/lI8dQmPGRyfz3JLhB1GjoYno6dCmO7HVxKxw1n+d2A3I/7ivWjkRdxhkSHySQGmHojcuQCkICAurluzu6R8o0USOdFPyq+L8t+VaLGQd9heamUXqZyVBFjPFrIW4c92c4PC3uTwz/MFLBoRydZfIkNfrJhFdMLZcNRLvCKtlEqN55BLeVl1Poxq5gcajqTXPtEEiruD83iGmpCAZrz5/smkS5xaJPDBpSy8OdrJ44NqA0sc3OfHNlEjcg0Yo67BKMsIZyZuWchOSKPlF1psaBc5UYANVElz0CZc/tYEGAWSVQF7BA+YmtWHJqG5SnJIFOrmAzqQ2F8qQhFwVxItU87QuaB6AFAYBjoHwnECzHURoAuAeeVcEwU+FIboOz60EUOuKQjrD536+uU6L/UAaR6i8zNfsJuzmJbSSsd9Ggi1ZnDrkV8i4oRrrnjrcMZGt4gCFfkPKQNvGb0Qz19HM8jGEmIEkGO8UYSbIYUqpAOtwAlIeNCwpxvEsgX5roFpCBMT4oQWbiNgGTJ3MDNtDoKi1KQpGcwEEk4SSgPEWWMMEEj999QLTVXE3AhKRNUFgiXRf8waChgNVsiRpcJHIBoC1VzYCXD0z6YZlZopKqCVROmiJ8sByfYdorgWU5ZslUEimIVg1KtMK2GkwSAmCtBZxuIhyMMI1PygORcoUSr+BdMaZPBwq1DPpt2RkmZBzouyubNRKBxhxMY2MZ1iuuGnqk9opUdZYLFZULMRsH/FIf4DrbxETJXkyMlkwMbRTwlJnT8LmR9Cwqg6CKhyXQLKWRbbp1KnfAshGq+TBSenCILbG8VJj1QsQ7GOeESbRgGgVwFMcNS95+TDhMLdIgAiqDEswAoIt7BeLeQKsytc4rFis0XLRjUQiS5JVEycH6gO+qAMaRrlvC5jgJQ7ItnBW2qZROpJCSthiHoZCk4ZavNqlgk15okuEA/GmRzPRnOTiW1IQEFjGO2urYYXIRmc6GOguRM47UDwNEBK36RNFZGtoCIplBngmQHOnjUxFxW+AHlMlKvhoDaCglCy8W2wQFvbk/Q1mSgFSOaiZYESK4XAQRQDOZAoILeC/DLTIHug5b5y6AfLRuLC0GgeN/27+IEw1Z3QFW+WrHbWH3BQBOt8MzrhtgshIQlKNcFcgkCXhlQmQX/FJohqxXbZlJSVEzucqDSHCNy//G5WGzOy9NLlPaA0ECCUAYBAkru92gptygLCKKXAIrQ2GamNN0E9uniiAiqV66dtTlASCbp0X5aF+kSo7T1CnCIALWKUwkFwkPbKAT2z8c9v4KvaS9NQ4WnlYrz5W+wKEKvItfQg15MIWEWAP9qWMD4Xaf4Uq/wDKrgE34FVbOj1imyiDps2pksQZSItc1VAPcqHYoQLIAlCyLVuHMCtaojyPUpZpHOPHcL8j7VqOkGuQp/k9UDCgUhhDjv6pC6BrhiAAdRHATBXB6USEZFzDUN4I1TeETo7MyRya89tAJTQmtK7sbHR3s0jS+unWc7SRgxfjWMhc7MgpYS017PrjDC5r3IXmR8TJDNylr51Fa+kdWognJbxoqs4A/YiAPerBhFDSOUGq8SQGTg0B7mfOUQBwHQ1Bs2LsbNbUIikuSNAgilyZTdqOGvah2VDwVjoHFNy9AjV+twP3zONhBwDcQb41zqZCC6x4TejEke6JJXXNdUCI0LiHYAkhssLAZjkGFt1V6+eczQPV2Ej16c6bm2jvUBHqD/hPOZyhvXSiz1xc2IqXJVpJEcNVfDA1gf3nSc2Nd84nhOv/XmGR6lh5emQcdVNc5C9I3GBkeXq/d+i66tw1AZgNulddoijbOCJ9L5a6GtYWYsA3GLwjbZm8iHmkZCDVH2yRhnA7kfWG50uNxR9sqUZY03zR1l3PA/ka42+8flYALqfVx4wfyviQmkJSzzE1adpMt4kFfmLdoQLYxYveMYEt/IOgcoO2byuxNdrj6cWBmmzhkIwV2Zl9SFFzKZrpYQ67QGWvfHXob15bKAoQe49OQdw4zdNkZHcdyyyGmwDSR+U9ojspkoQwtxSmpqUr+zOT8ysWlCOru4MVxCBPW7GOFPhOG6GwoJ8EqbqZgQnGBLmsrPFB5VbHyd5S31kDF6bu68dyykCrhPBzjMfIX/TZT4CcXeUVJvjNZoUEqAdHfA54NLcdm2a7MtQK22yQQIRwUTPJyzebUDSi13MZC4K33Qu0LU5CTOreomSTs1Vk7N2ZKynXTtvDQLkYPZWlSBKOk+UbgbOKBAguOhlCogFkeqvWRPx5E+FIO8XFwmq3S5Fdcu1HQ6YXWqJiTIUlLVrpARCKmmZ+mluFAOWnhoptJ3LWbsZP5lTsp4XEtlrpO8heEEJimJbvWMmx2TdpprBUv5YZCK9SJEBO1id1gINxEKvpt5mxNTQuEbu5dY4bDnDSfdVFT1okED3TMBtj2eRAyHTZrSOp2/SFm0o+hwcwQtpoc/hLhM9zuiqEovSgpZNa42THpjk02d9bl7XtIBzM/ru2j5n6TXZ+hhuPsooxPlAbLamecFBsSwQ0Y0EY2wPbMr+9HTQfVomH3iLMhj4nTT8oiUM4RovgMVZVXCEfsLgIq74yWN+N4VsF6JSVVmk32wC5VbPRVZQGVWjSQEZAQoATKt4ocihJqxGGaurD37rqYsgjjapf3PU39b1D/c6u/1tDf9nnWmb0UkXqyBDeeANSId1lQiEDmtZAzMNQN6HCjy0Pc+eaK5ZGzuORsdSsbMPPy6zVEBs2QcOH0G3GqqyIMwYCaSrzTmFn3JUGuRlH7hr3Dg4JZrW+cRLPqsS1rOEP1rAejaiJPIaNlF4lD5slQ6fzpQoj0aAxntRDx0Nai0DSliGfubUtgLsjxhyYyfNTWFH2MEMzY6pdKP2XaRi6+w4stHO0BmRLh3kT5c3W+HABQo89WnP3XJzD1kR0I6txlHzcLCl6kwvnKVHxGIjoxYAeqNSMKW5ocm5Sz8sSvjH8Ryx0w9Mcyt6WcrP5vsnlY/22HK1RVnLCpcWvHSF65VqhlXpaPn7e0j6pJB0YhNdGUNQRXo/+n6O7tBjlBcudGviubWwxFISa8gy0szX0Gax7KwZZkYLG7Rml6a3kZ0vprlKC1hMPxt2OodGehSn+az0kz/zJNoKmQpcbzVxiYFmms7bcce1AzjzmY9BYWWM1YKidSSyJvzpiZwnGxhndprdsIvwD9T5iMKpFWRN1Vvef28XazNiVN4C2bplCKZafF2s0EtZ2xVtpPyoqPF3rDbGiZZIUTAZx4gk18YRn6aXctoCsjHFL5sjptefJGn4pCiWkOykp/0sEoon0AIqUVGoLvudDf9PeZrSfWJBZsHbjx2KtM25Ddhvs5liuzJcrI2jknnh7UEifks36XiBzJjUvVhXXhcsGQZt6KkyFLaNK0AzSvINm1HmdKwQgLdMDmXhNmhbT6rZiDTCuhEypl+AcymkvmUaJGWI5vk3BOabaZopWN+3A1vX4jaO+bU3xhNnsUm5Mg4e05VqAuWJtgB3/dabcs0n3LNFukphRtLNDpsKoiUIFpz2RtKYcJsi2lUStqZ4myV4tkq5LezNCTcJVkxUqtoIHraJYGAKWG5OFOaaUBepvyb7dBgQqng1p0LKneuC/ZZmJVTjjnZDj2RLgTQdifIEkQxlxEmINagZaAt96vAaJ443iFQvOBvpTJ0c3Lavwr7TBgKhgK8uGoMgjtESk/O8bkWqoUpxvZO/JQX1SBWYa+wBJ9OcStUm7KkntgyPzvIcr99FtyVh2YuizH9bF5/cR3tXv6y1jkXi60LdXVJBL/+zWX0I8tCG/uklhtXrIE6IHI1wxuYGbbgIJg3rsnIG1Ma+uZWwbFtooIZfMTGX81/N8ywjFYM2H2Diuzg0rI13/5drIw2Q02shoBXnQQVrAkoc7WpdND+9Thn2uGsWQ5Nyjy4Ko+wNTXPZwN2a8/J0dBO9H1hl7hUcV2V21sq6ry/UeDVVXg0fhrq7usm5JpGrwR5q1N1atnrsnvhTJ4EViM9XM5fVxI9t2SMajFHuAQJzGiKBqOPZRPTR1Vy41RPmn8x8jNfIBupWPr4TrR5E8MoLWXrmPK0VDa/km3UIv8hG6ZAAVzQF79yPIgE8MoTWB8qNhg+4wxvY3kFzJ/G58aJuIycLAJ8mzRMpvdw7b9OD+tTa2r7ivx4a1AUcOqUMO/AhlVzIlPCWcVNFnNmQ5A7BU5MRJC2MxfvHpC/kUovacxMi0XJJmWWWoayeyxKUkqRbAYZs001h0B9DejJzit0Dny2huzhw/FnC8ScuJ+TWpwpV3rXhimhtht9wqpOa3yA7leFTRZPbDCp660G48xAAGY4X1kw+35NOYX2dT90lFw7aPEigaBJetF0SG20sCJShjtwe1FnZw60sTu4E6CAOX4vas+uLQVCH6pmhv41i2qVUBmnwvJWqW7bL2eWSiKvy8VAVlgR3bTFxSQUhw1XeGkFi6tskhqsMBLsx7w4nLopSWyHthsWXWAcXePeeXsvQ+RcBmP3dWksL72/kImSC3gVxZmQMJlu3ZWfO1hxy2IXFQDpyjN6wwDgHoLq6wCls2AFAJDmEIOcBIPgHbbTDAMoR2kYHFlC7Q/ZpmhD6ZwfZUP7bMZOD3NHc+5MacnOUqJbFpm26m5eni97WlDwEM2SXLtu4H+oKyu3FUsSuDnNekGS/bRlp2pEoHHG7zkMrBu7wBbtnc7tvbP3xFp5qsMakIFGq0ONlhi7fvNU4cWLHD6oRLNtWv6Gh+VgR/oiEd/7ykvBwQ4Aa9XiXQD0jhoxIaaMyG+OoayYUJzktRqDu6zwNJs/Uvw9AbYTjpxldGeBpxnv5/R4Y8rBMHnxLMMx3E7pGWPnLXB5MzwbcviPIPnlqR95YCTuHPD0hhx35ebVwj15yhjtaobCvWyvH5T+6zFYxEYfYAWH5KxpcGcaP0rBBwj7AGI9LXYn5axywk/ddJPnDKT1w5x8aNSHvD/XLdTVbzklOk5HU8bgU5cJhG1uy0o9Z1Yk/BEHPRcpNrU+Ov1O/HMnuTx+pSsTGhn+HlT6DbGe/WW5K1ix+9x/qYfVLtR32qk7EPGevDzRhk3SHOuzFtX5bpgpUEy+XWgefRwT5FcNQ+OGnBlOL1tdeshP3rSnlNaF+nXrv1PosXp/xltH49cP7T5T10h/0kGnpOjQTYJmE1/FRNJjdnpJpbD8XMjndaass/OMo2VNVx3Z6Do8AN3/NivHB3jbLEqqyb0TV8SQHfGwhKVgjlOJNJV4e2g5iWrJYx4rgP3nAx0O/L2Zwk6VfIcu2FY/YoAIrdYUJCZgVH4E5A78jKiUljZShNAlQHfSofis2qOmLqL5qI9D/UDbRINBrB5RuK75ggjsYYv8oD/1PONjNwfc1y5bT6+2bXIdte7yxakdtGJ0PzH25mCw0y1kN4k9fE1SUX8rvQzSSffzmaP8hTuoGaZHoZdf9W71l8ob0wdjta+mQoQ6FkGZ0mn/dm+JmGMBSAGvpoRUxrbIlbdCXfByAqbVu5M6Y7oVxJXJstoFcamhXP8O9+va21hhPx29qoOg4NYfK9LKQ6GUCxKTI6R2GVd2pN+KixxoyfIESifFxg+/cQH3kSvGZJxcgsXmuF4Ft+XaJhBoWPs0HfjDutxeB4Uf756X8bUcjX0HMBEyvAesqMT2TeEtTVyqyrh9HSh/Fq5h9fPRYG49Mdf13eBMjeNMsoGMHwSvk3B936LVlmskfe7JrAzFRwOQA/7rj9bSIL+V5sU4+wP0Rv7W1kgSl02Zg0t5op/24SX7yWBwYO6/zH4q2ALFV6/1hgYkqkRPlLXbEMd7v3wovWtsCHcre8T8vvAMDFFDP5iKHczKh+dG1xx+5T2oQLejJXckWQBCxkDBY6mfdRfFhzv0P3dh1upv3di24dpZB1TwZy6Mu3uQLXIpjA9RLVj0kc6jQz2bpfLaESccW1eLmCthPUKxRoxPNsS0NorNDx4YQ/GgGm8AvBTyC86vX9R69+LPr3YBSjZAJP4FZdnyGkNsOtSS8+udJzjl/DOqz3VcnGzxTkJAnJxZp3PYp3atSnCz3jkKnf5SqcnPeWgGs6nIa10M5oegJIBGA3ImYDQnLr3q92A4qE4CeFf2TllUAxyCrIDAv3zDEEvQEQqsAkJH34t8vKJDztfhXAiIB5WWQD0ArreLhK9lIXtVPpHAwwxq91HPD269eIXrysNIvAORQDPbK7wMCw/Beg9AaQDACVpggm6xddSvcIL8dIgsY2iC2nY+XMD4gjgMSCseVuXSVYQaun787OGvgMcZQbINyCuAZD0bwDkHWmKCpvKIOw88RWr1iDKgpIASCSjf2QMDKOK4TtBf3EKGrpRHP1X4NsAkAzsco5fANkcJhcNQUc+g33wGD5PHD0U8RgtgKqDLAmoKzUjLGMTRtGDWXhMdf/C4S0QWhRMVlBFMcYHZ5mNPp1Y0jgswPaQupKOEU53sOaz/wNAP4M9EBNT+X2NZnQ4zG8JNU4ygd32GmGiFopNIFIgfQYyAhNksWsA71xqJpij8ELHEIaoRwBHTmgUQ6AHNY88aAHwAB+BCzzwRpJ4FCQfTfJUgdh6BWAWAbAAfinBoAAfjEEJBKQRkFoACmAsg2QjkK5CRBBYGJhFoaQRsAFgGcCnB2wEQRnBwqBxGJgZwBYAH5BQlsCsB8kTkO5DxQyUIH5pQ2UPlDFQ5UNVD1QxlmFCdQsUINDoAGcFiBVQq0OJhYgEQWJgbAGcGvJFQimBPhNQ7UNFC1QmwFtD7QiSlFCnQl0LdCPQ8KnslM4TcVdgxeIVhbBIqV0K5D8kaVkmVOVbQTPYzGNExPtnQCaDoFYwtU2iBbAYmDVCJgCYBEF8kEsJbBpBWQVTDkgj/nMkGwPBQsgfQq0PEFJBGsIFDlMM7WOgUQzzELCEwiYCTCB+FMNshFwVZhUpw4PsPBx4wrULbDeQzsLrDQIYnCEB6+JBCDErdFCFt8KFNCixCA4FkLQEENKPjDBoBG1gaAXNGUGYA9yZwGChP2M1nBJsqZJmkUI+LkhvwrxTd0mkAlJQC8p04W0H1AoTHEIS4n3a/VNU33Ji2gDEYK1U4ciOO1UQDeHKYILFAPJXQo9BbBGEKsGgkiDJCO9CkKpCaQovFjZyQn1U5REUWNiwJzwAfkWhZwY0NkF+QiYECQEka2RUE/HHIlGs/8SuVpAr6UwIqDfgo6X+DhkQEN0tgQ0EOXwGdc9lPNUQhkHPAxgYCzCBzWAkK7JKBVnUhN8QlkP3B/rDr2+DuIjVCNZUvXiChMpGdgGogwieECQANAHSPnJTUaZ0hDYbOZ3htxNRGzhCLIakPSA88KZUbISqbj15B7JTgUGpcVGAUyBGmPfELJvw08FVxZARlg7puIKJGHB3QWQApgppNqE9AcQeE1DhmEKBEXZWRTDRcxFMbOGx1j9aRS3RJlUu1SDZ6MnliBcyOvTzxgaQJG8jicXOBfJd8d8jWQ0wF9iD0kYCqPuh5IlmWkA6NIHGT8EgT0Fl5++A8P1s22TqK3Rd/cNlK0PBNShLBldJ5gQ0TBDAC4RbEBsEVhMo2QCwiBIWkLJNtbWoETJYHJ7GqBVTRi3bB2wRaAzgQcLxFCixeeaLm4PoYRESjegLPE6R5I8RQshIoygGiiNqFwHrIbfVsgMxD8DqOcoDw96LKjgY7aKLxqotCRDIVcDAGYRcpZ+Eyiw9W0HNJOFU4DOjFoFaOOBLIXKLqB0NS0FzBaALaMQBKQiR1ZARpQFxqQfQbNxwoMQ3Qxd4pFXMOIVHBLgAPCPsJ0yuAxxFW3lU+AH/UHIhQ9kPnCOw/kMFDLQv0IlCpQmULlCFQpUJVC1QjULnCJY/UMNCZYk0PljzQuaHFjdQm0LtCHQkMOdDXQ90M9DGWVsL9DdYoMMdDDY8MM9DFyTHRwlpwsXi4UkTZaioNmwosJsASwgfjLCKwqsMXDKkEPjtAtgH0FD00mH5V7CakGoSUBXICzHaZ9maIEHDhw0cPQkE4xMIDCRw6VkAiDcEu1rY1xLsSZiRQXtz+xI4uMNTihw9OOTijAbvGEg7cKEAKopVDYFShnGSKVpA2AGAXd4GQWth0obgiiB+kQiQNlW864ukCFIPo/hi1Rvo2KLQkwHUmUgd2eJ0B+8dFOyBMgkAfsQWk44BLGLtqYJ6LTAzQByi8A69QCJyjS2U6POihSVVV1AogRaLfCFWWgFXDX2FHF188FPsOjArgIsX1VDIjmLdspXcbV8ha2RmLmgPYr2J9jKwhYGrD+QzOOtADvWkBO0jqOixfdIA99zlVg8L9xkQYIv9xllzUMeFEVZEc8Cci8I50DcieoDyKaNbIMf1Pwc4JPj5sMARpkXEfwsKNBwqAV4IoB84YegJIJ4l5lQ0x42dHYTfonPSjN6gkd3KM4w5yJyhXI4yOISTPEgG90DASADmgLICyBdAZEgRMcxcAcqMcp7oKqPMREUfyxxD4uWjUIUWYeLkkRBo+mEGs9AAeCUT5Ek9B4tHgmYNwSPhTaMIjnQedBESvAMRPcikATyKkTN/Pt239tOSaPUBlWeYBOFUHEv0NY0sBzwYTPQaSTs0MY0+NyYc9GkSQjcEvGKGJkYugBJiyYlYMNRZ4V+37czhAJJZBM7Q+OpiGQDvmcYMZRmLUjPgxY068tI9iHMj0oJICwZjUQEJiiXmMyKaNLI+niG8DjUbz/lFnbxWroDGAmLZEWYMYTqAtE2qPPcypItnkBSWYsW3wsST6IoAeEuKNHRM+HfiUwewgpRExfIWgG3iQoK+LQQagA1xYZiSQkEvYDvEEE3ZawUtkgs1KfwHaTM4AuImpyibaFXFe4EOOkV9E+jSlswKExLS8wwKUlkBbte9hLdRpNGnzQB4rMiyAVeGvXtdTkuoCRJ2qBXg185I45NpZFXBJUnp9ok6BtJFxecXttMU20AGh6YbkWSgNbNChIUxk/5MiUAAKSHDwqfgDwAqDYxMfEqtK4E3jxgM3nahaHXajtct+OeIdM0gX4FzFUAREL0daLPmXgTzqcCKQSJEKoVQTJZTi3/cP9BZNc4Rk5GKWiJk3AC0SW6PxxWS1kziOGCfg7SPg9mk5+k6Q2kn6NthGk4clIx1I2+WC9GMe1N4gHlQEMQNOkyRO6TBvH4ihD+khZxONQSaulzgsQfiRoA1tToEqSyFRFzEjTgWmK1VMHcNMQ0nnRA02kKEDXnWBok+kPTdymQFyaFTLIyCPZnYYuw8AZQCgEBAeU3HzigOQeMCGx0Y6kGiSUUgnUlxk+aHW59oSQ7A1sYKG9w19TgH7SrZok+gCFYi1JvhWgJWIpmlY2Y3bwF1+AEtNpQKQLJmrciQLElBpLXKLWvZR9NShM4V2Wd0HSjfRVVxAdELFNiAc3BU39cNeBfi1wjTcOClIbgSFS5w4qXxlVxWFeYGPCvMbRFVRDsboDGBZKPeBTTIE5vRiIHGcEF6g7WKsGbAXqOWDK1KU1CjTB8/esIjJGwzcyzFa3ZxgoN1NAqkpUF/ZBJXSfqMlWAdrcVAC6lgI5hzlSyhS1VYs4Arh1giuLVRHCpX4jcJFTs4CEDAjAPZBNgDlUuYPqE/xQwXPYwIIpOWZGLOwJHSw07EEgSEVYkNy4FhLoMMCSEz9RYDjg81MkT3U7YUPFvU1L2PRqktr36cNIpTLNT2IQdN4gx0/5zeJ+dRSGoSBIKyN6SA0gMSDS48KcDSVwqWtlXDbNFtJFwSgAKBwQAzMzOGBWMk1Cszpkig1v9xgIYJiDDMpIGMyVUDxSSAYY5cI8yU4QLVtILofm0fI0wfzLjAcdItzwkxgC3kElOWPt2AoDknBAQzsdEKLrZ2oZRELZxtfKG30AhbgGxh1tds1CBogZtiJT+wNoDmBpgHgTiho0UrTNBQsytIa1L9OBIgCKM4WXGyoImjLQTVUjBKgBGMlBJqEeMnhwapowig1kQY0omKHhKgM1jSVmovWyzSvM5rE6AH+aEglxZGPTOdTWAjVGizTMkNgtENAKzJsz/UmyOhCBkk4yczCyFzOoTBoASDRgs0nzLEyHsvaSCzlMTDK5imXBsEjBtyZFI2kpIqlMmQnlPKQ6kkBAdKzSBfMyXmTQoniHfje+XyiN0GKfXEgBsSdq2pc5oUbmkDrsP6I0w4FFwUvdcEj0m8DYcxYWpyr3QSTOw5tUQBZzVzcxGiBmclhFoA2gWRGiBGCUWGiBFyNqUAkLqVsT/Re0EpGxJ2wbaCnBwqJIAojYgDcRlDIAAADJrAdsDtgtc3XKsB2wVc1QBds+QD2UpoSsCZAuFZelm8o3fSWTEggRXzmhJqH8PnwoYSzTXEEYSaniz0c1FJGAO/ClEqBKQAbTxDGs/IEj4GwDaS1Yg+N5xhIYTAQA9AA4NAHTCzlU4Fdi8AUbJlTxsxi0oyH9LjOWyOLXjKQDIAfhIPTjsgqzmgPSR7LBzAvLiJ/Vbsq9JiySrOLMuyvggzPqSos5vLHTfsSdKgknsoQGsyek17L2T3shzLeovs5Ph+y3M/7MSzsEYdJJCJ0/7EHyBIYLMj8rgYZGuBxtRIXJJkhCLXGhcWETyMh72SgFZwklSEykRw7CCBP1j8Gpgsh100KUKy3yRs3Rd6JBFkuBwNarMOUVgCHGNlBQvFkFC/0L0LDAe2bcWqRbrCmLtgD0mkDpBTTJyTJVBgKkC5wWuTuHWTe4ZhHHD7sLzHQ4spI9kzITJYLHJxG4CyWG1kM+QHmjysRHJCgy3MAAPTw3NaOAKy2UrUaVQYPOPFhIASakXlJqbaEmplKHgr/I+CwykmoGoG3NOBv4MeUKpauatkVh/85gstYwwYCnYLowLAiyZM83ENj9pFAn1+BZoerJdhWsbvS0hICnIAbMBTe7XUBjoEgXBwSwU8OWpCgX4E19B0qpQbAAIw+EUgJSA9JOBXKPATtJHneKgPCXieFJdjT8/NXUKLfLAjgyzxEKDG04wOrCLZrkzmPxSzofHXnZs841VlS88ybKoylsn92LzVshjOT4K8hcSryH85fMlZV8k1Iiyu8u7KYE28p1KWNlMozJ7zai/7DwxKifpAHJ+88aFXyXsmGzHzA0+yMGSDAKfJQgZ8v7IByA8+8lkwHpNjE6KV8sHIwyN8oO1hBaFNsxXyPCrNJ0p+QPKNmQ104ENCCbsX1iqVTod5y/5fndshyzr01+LIKjbbHJ/i4BJpj3SECprEEkmYDoHhAWU3sDwB8oa2nVYbzEwtlAGs4rEqAvcmqm5z6NH6FMLzwAclEKzdXwAHJTEdEC2BWEGHMFzDhapB8BRURAAFyyWCyALZQYa1DqzAS/Qr+BQShkBg0GtDKDalxdAyySVeJD2EsF9yDGVW9u0+DJzYFEsfjCg6QRaEUApE/Er9YTimEj0KSIamCZcDWHQSqAmS3kthKObbHXQUHIF7wJkFTJkDyB3ncxFOwJg32DxKBTBzXBQ+mdQqJKooEkrOwDWBUqVAlSu0FVKfQa2WgKs01AARS1NMZj58dC3IArSS8DfhmLxIGKGNcR7P8LpztFJzXsBqC2DM/iS2ehjSLwAs6kyK2HSCOozuMvIrgjuLMvMKKjs4otWtPSvdHykui94AqLFMhvOI0migPJMyWiyVjizGwNJj3xVi/4vK1ylQdJ0zceAZ07zG8wsoJ1iy2LNaKMykQkOI5i8oueyR8vor9FbIo43G9hi5zNczxi+fOqhii6pH2JqiI4mzK8wBYs6AIc5YsqxKib0ucknkzklJJMif7RhKNiyYq2Kr3EfTWjG4LQGMKDhI4qSLTi+STallyAcgOU/nGJQN1NFKDPEBohHiFfN7AbiknKQuQSQNLnmTiAEgwAWQBgQokoEsbTmQaeRcB3ShsIoL48w0sJASSq1kGV7y/KUAot5THVXhpkBqRuL3CP0yZdopBTRe9sSHKGVLIAdsGzhrS1e3jtE7J7HIFWyP53DK/BLdkVUCUwkNhkJdOSm+8mwBwvELdyhEqRTadBnUhRhlKNguLQHWBJzyoysCPzzP3QvNyKEA+jPNRy81MoCh0y2cu7Ksy+YqHzKi8oObLu8ospby8dJvlLLseGpMbL8y8dRqL2y0soo0dK4fL9SBykTXszBiz7LHLfs9zKKKAoRWGzgFy+qCXK6o8HEqYuqVpnuAWEeSnwRi2LUF6lzEBhVYpkAKcDwAWAWGHOtFyBEupLl6NHMDBDyoPMjdkcl5TOA9VWhQvKtFRzVfN5VO6V8AJS/0EzE6VKaQcVG7fUDFUk4DJXqYppJl0ElhkXwr3yAoB3O35zoZXR9A3c08A9yWAat2Gp6kTIFnJ5yAQpGqnjGok9yucb3MqA5q93LEJrYZOC8A5qwECkABCs0EmpPpV5jLdegLQVoABC5LOjB0gNxgYE2pfflgBYS2aDqUVzUIPWo5gBflBg6pGSWikdKdPPrBV2Fc3Ahd2D9Lt9txS3I+L9QGqon5bQG0sgAz0+MBLt5+Zo1Bhks86Hn5Ii4wQOsszSCLtwMAT5PXEzdd4uVYTi8Us5LoarADpVaK9IAEktIdqPUTnKb/yPZR/DAByReY/tzyprVJGo+r/WG5IbBnq4pAFMSBSAFkBl4BO2prs2W0smLKLfpT+8mZdCk8FfSiNzHsCqmNyaVxa66HDLpU9ItzzZKrIoLylUovKUq1U5Mq3C1KrOArV7K3st0q8y01OqLmi2yqKYyyy6oZB+amKDI1IqjSSJylai4v6rVag4vrL2va7MaLDK1suMq9kUysdqa8hs38qei/suG9DGVyqgdJ8jytnyJignSmLq8h7OjqHK9fPLpKXc5nilN3DMo3KyVTKvbIhYRqHCTlWKgzjs3nPfFajZgo6RbFUc1JR1ZQ9T8wwqWVNNRnk8cppkx1dSw1ySkb2NYt35o8u3KlAVar0hpUB0zkqlKlASWvTrLNOKkxztxGIqU5fIBuqu0m6kN1RyrpZ8q6BjsSbAXEfM90llzVCwqsiUvgUoAEh6KtsH1y6S7Jl7q8wtWpaUiHUGE5zfIRHDTBUS5FLPKScotnDgAagWtajSC+lyxzPis+18hhSxrLMwiUtxmLqXoepgYL7NRiR8hzwHZCeU/i9cozB+08SsGJ7cyeup52FRSLG1Q2OwnOhAQKUhiB1qxavGrKASaqohpqucl5BNoNoGiAaGzRjxrlqs3FYbqG0ao2qIwLat4bogXapIBhGo6uSgdXM6vEa5mV5n4hAHIhDYb2U3hrNBogdkDaC6ANoHur3JX7x3C0wBgV+S1AAeP2hpfMhuwLYSDsyhdCC3aiyr0ER5JfUe6hErNBgKGhrvAJqqcmZAZqtBpwoQS7hoJrbXI0E5I6/UknqZ7S6NnXMjlVmEiSrCzhV1wA4GqrnrhyA8vTrzC2nQAd2KoqqkpWxCDlvs7SBslnExgbfRoAeoAv1Jki/ZGAP8HACqDR0S9Vip+TDk1eon9w7W3PbJZvQuqq9l+IKHMbfar0gjKQI191KE9a+SoNrFKujONrVKyYsrzVrKOtUsY6uvJMDbagypsrW81otUFnSyspYki6kevKVfiWRAYKObVsTr4Eq1c3z9282pM0ilm+2pWbSytHgx4aiByt6L46uG2HLYQkYrLzxyryrNrs0tuMzqOpDRjU57mq2rXzwci9E2Tfy2bXZZtmps1DKtQT4A3Ze6s3AKybNOfMaU9i1LHPKCg2SAQasgNZv2wITf+3AoXQIeEaUaQNQtZTvimpqjcsG3dBLqXiyAGJhzKMHzEb9S8lr7rqwGOL4R5KLgGiA4ATAADhQK7ACFIypaH25gckJ4HAMSASajUp701KAghpSJuEGUpc04HUDlmNHil1kmkXEoLfIeCAzAAsZykMaQ4pplLYKszPQYBu4MME3AKAW4DGA+m8jOjKIIzjJGb4AsZvmyTa38qmaK1W5oBa5m62vrzFmgspDr52MOpcQI6qCTqKLK/TKsqVjZZpMrWi2Y0p4HmuOr6TE6kcreaxiz5smbpy+NvNEc6kFrzqrgSpk51/tbFprKB8jVpTgV6lKJxzZmK22Qb/SxzSBNgy2utPL9ii8sOKWpR6tZgoW9F3cEMzKpDPL/aktophPqxBQPgrc2gB2zGWlFFSVo4tAESh4qJavq1yS8EuRSniq3VygRdNnSuZCjAU2T1JlBz0bDIajHORbopB/NbbMW+qEfNKzJvTkgDDdsmgMcW8nIarO7fzSVNsYKbB8ARIMACxB4qDqrHaUaVFruwsmL6ueV9QX6pqa13HdskLkSpfVpAeqfNobAomZgGJbg9RX2Zb36rppbJ/q9HM9BYoexrLBk+XRTQAqG8gDDFWecmoXqRcLwrjJeK86BKyko+YJ+RTeULGt1xdMjUJzw8HGNW9T7QZVOapK7WpkrBmmMsdan9WbJLzeHCZsXq0yitWzbDI3Nr9aqiy5qMre8syvLKgQH0CLbxdEtvio16tajdwA6q7IaLIsmNvDrWi+a2zqgWx5uTb5nNyscyU6icu8qh0n5tKK4TZr3nxc26DqAkom8uiGzDgpsoDaTOkNtWawWg9IhbnfUwu06GBOtsTFUG7puNlG2+DJCgbpNtoYrZMRuFsguTC/JXIrNdfFEAUW+kqpbIu+pl7blWEkPPaUqLFor0LIFDvsB6pFlq+KNC+sBIL+AKdolgyoVlvyhZ2+du06AkbkwskT2mkzJydWM9vRb/aq9pIAqzOAo0A9LRKwfbcAGGJnAbcoDvq7IG3QuJKRSj815rIO0EVMQZALzqpx4OvFRw7R9BViwB1/JoB3TN4cOBpANmB+rzYFKXsnr8VuilrW6jSjbuajjGpDrw7t2gjq3DTgd2q/tKoYLVBID09kGqy/S/wFh9awasDoSqs2pxnLqywULvNPXWNgVKlsIWu7jUuycoLYg/Y3QrMJupvT9yFWhroFMgmD0u7a3oOBsdKZXOE2Cbl2nbr2ktayMpKFWHB1sVTROlVPE6kyyTs8zpO17nM7Zm+ToWbFOgLqubY21TrfaUE86XKVdO6MCzSGCkbpeKDi2yFOb6iupKU7Q6lTsdqwbRNqcqnmocphCHItNo+a58xzug0qgH1uBbFihDsqZo7cxGii4daBLLzi7WlNtApgNEnAFonLirioouxWv+0emsNh7g6K7NkgBo7RDJB7UynLq7F3MxpWjtdHY8qBqQMI7ueKAwGrtJbatQiobBo7bdnlL+da3HrBGYNKOqlXGwzT9cZlMeouLUAdRt9LZlYu1hLgKMPux1E+/6CIQ+mVGsQUHw0hvSycxPGtbizcE2AYEqe5PrK0CG45pX4D6o5WVY9WXmrQqW69AA6AlEczhVqdXXLzZr5Gm4FrA2dYmj5jjYYPt/ETGaOxA7EWuGtkQua2aRBwmbKKmkp/BbsA/As4YPoCh1SiRvX66gOgEFD2UwUOr6mSPEtAbVuXKiDQ6s7TE51PfcpRQrg2F6r37CZT2s47tWzsmG6uKG8OgANeYYg0BlKPvCnAH+qRPr7KgQMonclmPJpylYAMloa7OumMXnbv4D7rFh6wQ/t76ZJG/FQAhasWtfqAoJVrwaOYXpqZ7+mhBPlSpsuMsNqXW0vJ56U4D1te4deoFr0qk1UXuU6Syx2txaKyqO2Zt4+92u/hZespNCT9m5WrH6OKMMG/h/as5ssr/W6yrF7TO0sqa9deiENsy3sgYqTqjAY3s8rTer5osgzB8Qbzbgq7U1CrKLJRHRAGBGFs0xPnUwvY6hSB5Wyrfyo8pab8Gieq0HA+0ApLtzSWC2PjrisBsaaf4lquYQ2qslRA6DWcDrAHn4A6C77GfO0GQKbG1psiHkpWksrqmAJmvMxeqxdkIbyGn9MPqeAfhrBAdWychNgmG+cj3Jmh0kr8bd3Vxs2qrEPflkaugct2kaSOdCBf7+gPfgGirwwtln5W+52tDycoKBS8QhSe6serrgeYed0WSl+qTtIgYDuJqCUqRWhz4ikez/rmfLWyPbJi6PrnzqSlftlMbOWkAEgKmlCBns+EHam81cc22WH7dBh7Ez7mIWqned+Y7HxpkrC7mpJrl+TRSOqRhglxy5fGugYJrelb4BJY2gnew2kbhz4YX5T+tlxop+YjYZOqK3T4berpYT+qcwFEUEbLTd2sgXbqZ+nlVCEkC47ttaMi3WuE72e6CM578ilSpTLM29SorVnBoplzKFO/SqkHNemQbDbzK3TI7yo2++UC7aEUNvGg6IbYV69GcS3qs67MmzpsHRy77JN6063nu8zF8jUrJFlRuzkt7c6qIF87ws4UaMHpBh2vFGQurNIha5WmmRUGi2enQWk1/fiwrrrNPLo+AxCtLvK6lWVAdRGMAXFvA0CWjakQA0+urtBgAK0aKyD9caKRT9gQh+sg78gZMknD0dWjqcEYxjrv2SyBzlr3iD4igYMwulArSJHBQKX3zU0tAKGR9IRyvtoGVq1mH+jsOwMFw6Lun7vLA+mRNDpASm8tthAtWs/HvAcgPVoDgDW5JiNbysm4FCj2oc1rucrWu4GHJOBu1pZG2e6bPjKja11qEGFePnrkIPU40fORTRm2pF7rR0UdtGFR+yTihgKBSqRhzeH1j4BVs4mjg1M0yYv079ByNsMHo24waC7SyvcY4DzBvY0sH+ilNteb7OjNqk6WB7EYuL1/WnwygGBTztYQXOe0cmLHRhdvhHQMrhUT8+QcZVr8pI18obHj+iobwrwG8GvyhCuintW8mQVGonaWumZSZasmLrveHT8TfjTAYJnfnRaUxmZS2YgPT7FXJ04VqNb6cxkgYuhsqQzU+NMsn0ATj+LUkF4nOsoLE/aKwGAVmhFwLKlCBN6kPHJwyE6IVK0fM6xrJw9oLSezGgSWkEBGfQMhNLYSurJkjiSQH6qkmABYIAsaLSMCIMnvmpJouHGw+O3+0ZwO8hIzqfeicz7whmKC8n+UHUiKbngt7sazKOlODB6spSt2x0dmTKWOG0WpXrbaMulqQOVqC8AqHbGWB5RnBaAQULH94gctmgkT4H/XOsp4/juZ6b9ITtXG+B0ZvQTBB7kfAnza17iDrjOr8blHWi38csDc2xqivGnW3lP11sfRMr3I9EPbIrzZEVQYx8PR4qAPqsfFfLfHWpu2ptHW8j1LfKXeHiFjq9e6zrsjNRuwdTrfyoHIpzthNaY/KAq62ut7wccGsVtmeV8sz13y3MFxr8arFIj7rhoieSG4wDRQuL/naAa0kUG+nLQb6Sw0cGJcpxRFOnUpyrSy7Yx6hWPIrgfxApkXM1LkOElgcKjMBFyKmuYHmWj9tSlXef+xHAR2Pwufzxga2Q+HMhK6Bi6HxVnkJnZlIlXsUS+rjupcFYMeUWhHnfxBWglOGnJa4ASVmYa050fdr/7wovpisn04fUGIqlQUirUpzwSivec6VcxAVz2wVGYwqB6qsVZaq64PPaYBtO6qhAx5cCB29BS4bCcAfpRPOTz++fGs2pIp2EGim9semaxmRIOrDinP8iE2UR84MoBrd8AOGYBAEZplEFCmZlmchQ2ZniEFDTZugG5mYIaCTe4uZv2Z5mQCkdNgBfYT2YqQmRnWuqmFUtcf4H6piTsam9R5qd3Hjpu6fWmzp4FqFHJBk8aDax0iUYbL3x48c/HlpkyvRqHcTaYsHR8wcvHzbO5Ou1H7B3UeEHpyuuZGzAq2kX7cssy0eLnq508dby/c1AFB7q22AYlsNpGE1GitWsAsQtIkoNz7iVyZZgD7dhLLsraCK/9pVxycLbhPLa4cVxVa02BNzT1UWyyBatC5T2lsg26tGDuwfofUAHte2tMPmA22E3gHiJYMsiyYP6jpjqHRgdKXrYzkPfCDn6AJmpcx0ZvYeZbuUigCZa6ah8gPjGLZCH97/5kIYPSoYPWYFxpzMUD9hupJCJHGI7LuwZBogbEjkoty8FJUawwaIFXBLgM4GRy2gRqhpBcEJMDeVg4aNCkBE5wTtZ6U52qedb057nszmu53kde4e58gEFHheq0ZHnS52ovLnA6ozqWnR5kyrFoC6MsgbmAJpuZcqNR1NtAmHByYsOmLIZReMk+5lcqunjNFec2G3Rx7rqx0K3eJknzZ7ISSGq22rK6YfpxiiUmhu98w1LqrZaTrJYSsgCexsx70fczWBsqoZySVASpsWdB3coMmOKpJjWiXPcGdfBdJ2fqIxgO3Ar2x8CtdJWJ8pYMxqYDcXMb0aQoJOyLZByahZnAdcvXOiBtoaIDlmqlmIBVy1cxaFiAJcszFFzMiLrN6aT+xXGiWZJtJYhgMljMLS7MiQqbXJcl+uHyWhJ4pbwk+ciVp3JcAOpaLD643koWXkZhZZvI7yLzIpAWlxhYJyYgaomFy+AIsOaG2gXNhFy8cWgBEFNl0EBaXvMPyDJn35/zUKaxEHGaDLPGNBc2Kg8ytty6vKhaXiKowKQFl8f423wYE48zBaNmZQOfNSHMsYppgSwArgYmzWR1Obqm5shqdNqeR7Odc5DF1Rfma/O6UbyNZRvSHDbJR85v86jBjwVBAUgfFn6R9IxpGTAHcMACGxcAIQHqhmVtUasHgJhyNBIGJ0IC8EE7GlDWH8Qg4qx7TwOgsIBTwe0HxDaVotiaq5KAhQhK8fRwRPKhqhkBCUhVwYG5Xn+BIAhHg4FLvVX8x7plBJ4II0DAAYTVyUujuJxIrOzfvQkzmgIAFrGogUpp9ru1D3VMVp07okJTVBDkNtvVIfKiAGEgHVi9tsgBFF1Ye1ewuNEnCqAvupVVopDVd5WfBO2A1W+2aqFwbBGR1ZqAwxrHrgEGKI1ZJATVxu2PIiMFmJ393o0LIbY8pw4Re98KfxaIp2WudveHFybZCwAgwIAJPxiBEpjbbs9YH2cBupcpM6AyE85WXgICwNbVNS1hrXLWTlKpDtWRwANYq6bse0sRzW+3RRKYm1lXyi1ZpSdYZXCAZXt3aF14LXxhDoIHolNCoDwB+YGBEVMUB/4hnSxkigKhXrBgKIVYYHAGoWQ5aCtWnPrALIKdZOAZ1pVlsh/F7NkOwCKWBy5j9VkIdGB0XHVoax+owFLWyTvRIpchJ/C0kckk6jxGvXZAA1g1X+oiTLTFbyuyjbbOFlnqgCeFnIr4WUV3h1iBbxv41sSXVPxNnFxwu/u8F+VrbIqNu1myScASQdsAa10UIgCIxGbGtxkMJBojTJXNqSlepWELTY3fRpV8gAZWQgZlY0BmV2yDXwNVoddnXbIJNagcdOOaFHXyActa5BBBFQQ3FQhXBM/W1PbdZPg/VrdcdWzii9Cb4L1shQM6pRj8ZlGsCSdXDUwAI/CUhnN31MbnnKkbw5Whit5uzJVMWTEFdP4YYjNGxea6P+iHFrVkx6qwa+zhwwWwLc8Z54nn3Yps/fNRiXe7OOAKpcdC/BgKcLY4tVZ2JNdeMhHAOHQiqD8okHT9guPFg3FF5yNa9GzdGkHZbKSpPgRAR0hJYxcgy1CjQ30ZCJZXM5cpch6Wip4kjIBhQZub8D35/hDyYngUnPMIGZtARXLUm+DUhQKZCgX/a7QFedPqVzcfpTYRtdNjyzp4SZWNoFAwAVvneJgU3mixeItqPmcTXiQoBfwKgHQgD5vvkS2ikoLa6BSIMyxFB/wwluW0Q6MLf8o5MkOh1hnNtuGtYY03rdoEzNJceZHk53geI3aM/hdlkScQsmvGktnyCFCzfULarwBNkw3xXnNgo0QM3NpvA83kweqAOFmsm2d/sMfQVP/QlEqAGGYTYUopJNpAMZZMZ6JZ/JN85C8xHQIC2N7jQKxka2Rx7QCqqtTXh1xql8rZ5DqQ5mnB2hnoYp42RMZ2xdgxbKdEllsw+h6to5GQTCl85P0E+9fvgfzsl1HNl3qiEZbyhFd3QHpaxd7JwZn84AURUC60QUMpyU0L0md3Cna+bO39wJXet2VJIviKcztrra53it6PghJXSFhmjg1ei5qSAfAScCEq6CnvI2iDIounNy1F6G316W5zUbTc8U8aZwoNotkj/gl+SDnZlAVDwQYA0YOSIPDtnKBS5iGm+E0qYodDak23/kQCMG2+daJnPA72B9gvWYU3NMZD80vSYpNpFP9BlhqpOVddQNxdlKGjzVx/SPdjoficSn/c1tOd60pRDrtL0l+dO0FabJgCyp7zOby1Qqo2XcLGNEqGIphJpwfZxDZvHzIrXXYBiE0kC2X5In2AUx8QhTQa+ExBSwU9OEaUlZ2GJrs3l6PMBiBqcbZ31nNZmPQUwEEhwQnO3CUhJSsAMlLPcwi/Gp8Szugd205oCyvZ47Qgc/ZbGBqegGcZIgTlSdA3IMCkAdsswbFkQ0tNUB2YdFW+LXCuA2HaTnuFhHevGkd0je56mM/+U1WOelbMTKdsp8b2izk9Bfi6Kk2zaJj7NkldCdPVr2G1TEW1laTb1Rnae0XCyT1fsBeo0IAUTp1WA7k251oJIXjwcZjguYKN9wmxk12vBJcii8QhJMjEALxIvHKEd2Gq05gRlLYQJMwIV66JScfb9YlET/2ikHD3ACcO+JFGLO7upLJFIgeSzGW8Sv7QDHSjT21xPcSiEzxIUyRFXFUElo41rdu3qJG3Lkp2/Zo20bg94OFkjsQ6/ciV/QaCwAitWEhGp8PeTvozBwO9mNdHEirTsK0CqgeRCGPD3rH6XN99PJcEkSZeqeJua2cgEgDWdaItBKFLaJ2i3aP0upAmjC3zgUsxgo/iaatbOyqkkYkY6yjSU6QHna7KSgjvxX0hfxeTX58YAi61KFQ+yPr05Vq+3AjkLgFMvgBcfoBRleti3AZ3akDUOzCgPSFBbwXJVIkzWMMFkV07C+Y20l9Iw6X0sWfDaqmmD7IpYOxOzkYWyODxZy4P2Rng64sSUWd3Ah9szfuBA/YfzRUO2jyvKWpBla8bkrwoezcMADAYCFwO7QOdsvAQlCDe8RkF97M/BeSqgBwg/wfCEAhST48Dcb1AEQSBBEAEQXgU4SOgBEFOYYeIIgyTgAHYigCU+2ASAZFloAigZFjQAAAVllOeXIBbFPdgEgEVP8WbYAAAWAQF2BdgHlzQAeXAQG2AGAIoB1PNTnlxChRTjk/gguTnk75OfTAU4uXxV/QCAA=== -->

<!-- internal state end -->
<!-- tips_start -->

---



<details>
<summary>🪧 Tips</summary>

### Chat

There are 3 ways to chat with [CodeRabbit](https://coderabbit.ai?utm_source=oss&utm_medium=github&utm_campaign=AIstribute/Claro&utm_content=698):

- Review comments: Directly reply to a review comment made by CodeRabbit. Example:
  - `I pushed a fix in commit <commit_id>, please review it.`
  - `Explain this complex logic.`
  - `Open a follow-up GitHub issue for this discussion.`
- Files and specific lines of code (under the "Files changed" tab): Tag `@coderabbitai` in a new review comment at the desired location with your query. Examples:
  - `@coderabbitai explain this code block.`
  -	`@coderabbitai modularize this function.`
- PR comments: Tag `@coderabbitai` in a new PR comment to ask questions about the PR branch. For the best results, please provide a very specific query, as very limited context is provided in this mode. Examples:
  - `@coderabbitai gather interesting stats about this repository and render them as a table. Additionally, render a pie chart showing the language distribution in the codebase.`
  - `@coderabbitai read src/utils.ts and explain its main purpose.`
  - `@coderabbitai read the files in the src/scheduler package and generate a class diagram using mermaid and a README in the markdown format.`
  - `@coderabbitai help me debug CodeRabbit configuration file.`

### Support

Need help? Create a ticket on our [support page](https://www.coderabbit.ai/contact-us/support) for assistance with any issues or questions.

Note: Be mindful of the bot's finite context window. It's strongly recommended to break down tasks such as reading entire modules into smaller chunks. For a focused discussion, use review comments to chat about specific files and their changes, instead of using the PR comments.

### CodeRabbit Commands (Invoked using PR comments)

- `@coderabbitai pause` to pause the reviews on a PR.
- `@coderabbitai resume` to resume the paused reviews.
- `@coderabbitai review` to trigger an incremental review. This is useful when automatic reviews are disabled for the repository.
- `@coderabbitai full review` to do a full review from scratch and review all the files again.
- `@coderabbitai summary` to regenerate the summary of the PR.
- `@coderabbitai generate sequence diagram` to generate a sequence diagram of the changes in this PR.
- `@coderabbitai resolve` resolve all the CodeRabbit review comments.
- `@coderabbitai configuration` to show the current CodeRabbit configuration for the repository.
- `@coderabbitai help` to get help.

### Other keywords and placeholders

- Add `@coderabbitai ignore` anywhere in the PR description to prevent this PR from being reviewed.
- Add `@coderabbitai summary` to generate the high-level summary at a specific location in the PR description.
- Add `@coderabbitai` anywhere in the PR title to generate the title automatically.

### Documentation and Community

- Visit our [Documentation](https://docs.coderabbit.ai) for detailed information on how to use CodeRabbit.
- Join our [Discord Community](http://discord.gg/coderabbit) to get help, request features, and share feedback.
- Follow us on [X/Twitter](https://twitter.com/coderabbitai) for updates and announcements.

</details>

<!-- tips_end -->
--
author:	coderabbitai
association:	none
edited:	false
status:	commented
--
**Actionable comments posted: 8**

<details>
<summary>🔭 Outside diff range comments (1)</summary><blockquote>

<details>
<summary>apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts (1)</summary><blockquote>

`84-130`: **Eliminate duplicate instructions extraction.**

Instructions are extracted twice - once in `handle` (line 84) and again in `buildMainMessages` (line 130). Pass instructions as a parameter instead.

```diff
   async handle(
     validatedIntent: ValidatedIntent,
     context: ShipmentContextWithServices
   ): Promise<ResponseFragment[]> {
     const instructions = this.extractInstructions(validatedIntent);

     // ... validation code ...

     try {
       // Step 1: Build main messages array for consolidated templates
-      const mainMessages = await this.buildMainMessages(validatedIntent, context);
+      const mainMessages = await this.buildMainMessages(validatedIntent, context, instructions);

   // ... in buildMainMessages method ...
   private async buildMainMessages(
     validatedIntent: ValidatedIntent,
-    context: ShipmentContextWithServices
+    context: ShipmentContextWithServices,
+    instructions: string[]
   ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
     const mainMessages: Array<{ content?: string; type?: string; priority: number; attachments?: any }> = [];
-    const instructions = this.extractInstructions(validatedIntent);
```

</blockquote></details>

</blockquote></details>

<details>
<summary>🧹 Nitpick comments (12)</summary><blockquote>

<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/details-header.njk (1)</summary><blockquote>

`6-6`: **Use semantic heading instead of styled paragraph for accessibility**  
Screen-readers interpret `<h*>` tags as landmarks, while a `<p>` wrapped in `<strong><u>` provides no semantic cue. Prefer a heading element and drop presentational underline to improve a11y and keep styling in CSS:

```diff
-<p><strong><u>Details:</u></strong></p>
+<h2>Details</h2>
```

</blockquote></details>
<details>
<summary>apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk (1)</summary><blockquote>

`6-16`: **Minor: trailing blank lines may confuse LLM prompt packing**  
Unnecessary blank lines after each intent block slightly bloat token count. Removing them keeps prompts tighter without affecting readability.

_No code change required if size is not a concern._

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/shipment-identifiers.njk (1)</summary><blockquote>

`12-12`: **Consider refactoring complex inline conditional logic for better readability.**

The inline conditional logic for comma placement is complex and hard to read. Consider breaking it down for better maintainability.



```diff
-  {% if shipmentIdentifiers.cargoControlNumber %}CCN#: <strong>{{ shipmentIdentifiers.cargoControlNumber }}</strong>{% if shipmentIdentifiers.hasMultipleContainers or shipmentIdentifiers.primaryContainer %}, {% endif %}{% endif %}
+  {% if shipmentIdentifiers.cargoControlNumber %}
+    CCN#: <strong>{{ shipmentIdentifiers.cargoControlNumber }}</strong>
+    {%- if shipmentIdentifiers.hasMultipleContainers or shipmentIdentifiers.primaryContainer %}, {% endif -%}
+  {% endif %}
```

</blockquote></details>
<details>
<summary>apps/portal-api/test-classification.js (1)</summary><blockquote>

`7-7`: **Consider using proper service tokens instead of string literals.**

Using string literals to retrieve services from the DI container is fragile and not type-safe. Consider using the actual service class or a proper token.



```diff
-  const emailIntentAnalysisService = app.get("EmailIntentAnalysisService");
+  const emailIntentAnalysisService = app.get(EmailIntentAnalysisService);
```

Note: This would require importing the EmailIntentAnalysisService class.

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/validation-issues.njk (1)</summary><blockquote>

`15-19`: **Consider a more maintainable approach for document type mappings.**

The hardcoded document type mappings make the template harder to maintain when new document types are added. Consider passing formatted labels from the handler instead.



This approach would allow the handler to provide formatted labels, making the template more maintainable:

```diff
-    {% for doc in validationIssues.missingDocuments %}
-      {% if doc == 'CI_PL' %}CI & PL: <strong>Missing</strong><br/>{% endif %}
-      {% if doc == 'HBL' %}HBL: <strong>Missing</strong><br/>{% endif %}
-      {% if doc == 'AN_EMF' %}AN/EMF: <strong>Missing</strong><br/>{% endif %}
-    {% endfor %}
+    {% for doc in validationIssues.missingDocuments %}
+      {{ doc.label }}: <strong>{{ doc.status }}</strong><br/>
+    {% endfor %}
```

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/rush-messages.njk (1)</summary><blockquote>

`11-39`: **Consider simplifying complex conditional logic.**

The template handles multiple customs statuses comprehensively, but the extensive conditional logic may impact maintainability. Consider extracting some of the status-specific logic into helper functions or separate template partials if this becomes difficult to maintain.

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts (3)</summary><blockquote>

`345-352`: **Consider removing unused formatMissingFields method.**

This method appears to be dead code. If it's being kept for future use, consider adding a TODO comment with a timeline or moving it to a utilities file.

```diff
-  /**
-   * Format missing fields for display in status messages
-   * PRESERVED: Existing logic unchanged (kept for potential future use)
-   */
-  private formatMissingFields(context: ShipmentContext): string {
-    const formattedFields = context.missingFieldsAnalysis?.formattedMissingFields || [];
-    return formattedFields.join("\n");
-  }
+  // TODO: Implement formatMissingFields when needed for enhanced validation messages
+  // Reference implementation available in git history
```

---

`183-198`: **Consider adding more detailed documentation for the status message logic.**

The conditional logic for adding the general status message is complex. While the comment explains what it does, it would benefit from explaining *why* this specific combination of conditions is used.

```diff
     // Only add status message if:
     // 1. We should add customs status OR missing items
     // 2. AND we don't already have a release status answer (which includes status info)
+    // This prevents duplicate status information since release status answers already
+    // contain comprehensive status details, while other question types may need 
+    // additional context about the shipment's current state.
     if ((shouldAddCustomsStatus || shouldAddMissingItems) && !hasReleaseStatusAnswer) {
```

---

`344-352`: **Remove dead code or add specific TODO.**

The `formatMissingFields` method appears to be unused. Dead code should be removed to reduce maintenance burden.

If this method is planned for future use, add a specific TODO:
```diff
   /**
    * Format missing fields for display in status messages
    * PRESERVED: Existing logic unchanged (kept for potential future use)
+   * TODO: Remove by Q1 2026 if not used
    */
```
Otherwise, remove the method entirely.

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/acknowledge-documents.handler.ts (1)</summary><blockquote>

`57-72`: **Remove unused validatedIntent parameter.**

The `validatedIntent` parameter is not used in this method and can be removed to simplify the signature.

```diff
-  private async buildMainMessages(
-    validatedIntent: ValidatedIntent,
-    context: ShipmentContext
-  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
+  private async buildMainMessages(
+    context: ShipmentContext
+  ): Promise<Array<{ content?: string; type?: string; priority: number; attachments?: any }>> {
```

Also update the call site:
```diff
-      const mainMessages = await this.buildMainMessages(validatedIntent, context);
+      const mainMessages = await this.buildMainMessages(context);
```

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/process-document.handler.ts (1)</summary><blockquote>

`392-402`: **Use optional chaining for cleaner code.**


```diff
-    if (cadData && cadData.cadDocument) {
+    if (cadData?.cadDocument) {
```

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/update-shipment.handler.ts (1)</summary><blockquote>

`162-169`: **Consider adding specific error handling for the shipment update operation**

The `editShipment` call could throw errors that would benefit from more specific handling to provide clearer feedback to users.


```diff
-    // Update the shipment through agent-context shipment service adapter
-    // This ensures proper Candata updates and email notifications
-    await this.shipmentServicesAdapter.editShipment(context.shipment.id, updateData);
-
-    this.logger.log(
-      `Successfully updated shipment ${context.shipment.id} with: ${Object.keys(updateData).join(", ")}`
-    );
+    // Update the shipment through agent-context shipment service adapter
+    // This ensures proper Candata updates and email notifications
+    try {
+      await this.shipmentServicesAdapter.editShipment(context.shipment.id, updateData);
+      
+      this.logger.log(
+        `Successfully updated shipment ${context.shipment.id} with: ${Object.keys(updateData).join(", ")}`
+      );
+    } catch (error) {
+      this.logger.error(
+        `Failed to update shipment ${context.shipment.id}: ${error.message}`,
+        error.stack
+      );
+      
+      const errorMessage = this.buildUpdateErrorMessage(
+        `Failed to update the shipment information. Please try again or contact support if the issue persists.`
+      );
+      mainMessages.push({
+        type: errorMessage.type,
+        priority: 1,
+        attachments: errorMessage.attachments
+      });
+      return mainMessages;
+    }
```

</blockquote></details>

</blockquote></details>

<details>
<summary>📜 Review details</summary>

**Configuration used: .coderabbit.yaml**
**Review profile: CHILL**
**Plan: Pro (Legacy)**


<details>
<summary>📥 Commits</summary>

Reviewing files that changed from the base of the PR and between e5f3e38af0ca910ff72f9d569466e97ac53ed924 and 7f7f6e1df1a51d3efe79e5264b993a3b6cf4e53c.

</details>

<details>
<summary>📒 Files selected for processing (37)</summary>

* `apps/portal-api/src/core-agent/handlers/acknowledge-documents.handler.ts` (1 hunks)
* `apps/portal-api/src/core-agent/handlers/acknowledge-missing-documents.handler.ts` (1 hunks)
* `apps/portal-api/src/core-agent/handlers/base-intent-handler.ts` (2 hunks)
* `apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts` (1 hunks)
* `apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts` (12 hunks)
* `apps/portal-api/src/core-agent/handlers/process-document.handler.ts` (3 hunks)
* `apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts` (2 hunks)
* `apps/portal-api/src/core-agent/handlers/request-hold-shipment.handler.ts` (1 hunks)
* `apps/portal-api/src/core-agent/handlers/request-manual-processing.handler.ts` (1 hunks)
* `apps/portal-api/src/core-agent/handlers/request-rns-proof.handler.ts` (1 hunks)
* `apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts` (1 hunks)
* `apps/portal-api/src/core-agent/handlers/update-shipment.handler.ts` (2 hunks)
* `apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts` (1 hunks)
* `apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts` (4 hunks)
* `apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts` (0 hunks)
* `apps/portal-api/src/core-agent/services/shipment-response.service.ts` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/details-header.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/document-status.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/main-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/acknowledge-documents-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/acknowledge-missing-documents-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/cad-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/document-processing-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/documentation-coming-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/hold-shipment-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/manual-processing-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/rns-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/rush-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/shipment-update-error-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/messages/shipment-update-messages.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/shipment-identifiers.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/status-line.njk` (1 hunks)
* `apps/portal-api/src/core-agent/templates/consolidated/validation-issues.njk` (1 hunks)
* `apps/portal-api/src/core-agent/testing/e2e-email-pipeline-nestjs.js` (5 hunks)
* `apps/portal-api/src/core-agent/types/response-fragment.types.ts` (1 hunks)
* `apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk` (2 hunks)
* `apps/portal-api/test-classification.js` (1 hunks)

</details>

<details>
<summary>💤 Files with no reviewable changes (1)</summary>

* apps/portal-api/src/core-agent/services/intent-handler-registry.service.ts

</details>

<details>
<summary>🧰 Additional context used</summary>

<details>
<summary>🧠 Learnings (37)</summary>

<details>
<summary>📓 Common learnings</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:1-18
Timestamp: 2025-01-27T23:11:25.008Z
Learning: Security validation rules including PII detection, shipment reference validation, and suspicious input handling will be added to the email intent analysis system in future PRs.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk:12-19
Timestamp: 2025-05-26T14:29:32.717Z
Learning: In apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk, REQUEST_RNS_PROOF is intentionally omitted from the classifier prompt even though it exists in the EmailIntentType enum. This is a temporary state while the feature is being developed - the enum value is prepared but the classifier shouldn't recognize this intent until the backend processing is fully implemented.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#80
File: apps/portal-api/src/product/product.service.ts:331-333
Timestamp: 2024-11-06T18:54:38.968Z
Learning: When reviewing pull requests in this repository, note that fixes for issues may be present in later commits within the same PR. Before suggesting changes, consider that the issue might already be addressed in subsequent commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:102-133
Timestamp: 2025-01-27T22:11:26.710Z
Learning: The email content template in `analyze-email-intent.njk` requires security improvements including variable checks, content sanitization, and encoding handling.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#332
File: apps/portal-api/src/email/email.module.ts:72-133
Timestamp: 2025-02-03T20:04:38.211Z
Learning: The commented-out code in EmailModule (apps/portal-api/src/email/email.module.ts) is planned to be removed in future PRs. This technical debt has been acknowledged and scheduled for cleanup.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#159
File: apps/portal-api/src/shipment/shipment.service.ts:741-756
Timestamp: 2024-11-25T18:13:28.100Z
Learning: The temporary port code fallback logic in the `submitShipmentEntry` method of `ShipmentService` in `apps/portal-api/src/shipment/shipment.service.ts` is intended for the demo and will be removed later.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#472
File: apps/portal-api/src/commercial-invoice/commercial-invoice.service.ts:216-222
Timestamp: 2025-04-08T13:20:07.264Z
Learning: The validation preventing modification of commercial invoices for shipments with customs status ENTRY_SUBMITTED, EXAM, or RELEASED has been intentionally removed. This is because subsequent changes to commercial invoices will now automatically be updated to the entry through the synchronization mechanism implemented in the EntrySubmissionService and related components.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#174
File: apps/portal-api/src/shipment/shipment.service.ts:517-523
Timestamp: 2024-11-27T15:29:18.862Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, if `relatedCanadaTariffs` is empty, it is intentional that all invoice lines are considered to have invalid HS codes.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#575
File: libraries/nest-modules/src/entities/commercial-invoice.entity.ts:133-137
Timestamp: 2025-05-23T18:53:53.053Z
Learning: The DeleteInvoicesOnShipmentDeletedListener in apps/portal-api/src/commercial-invoice/listeners/delete-invoices-on-shipment-deleted.listener.ts provides robust cleanup of commercial invoices when shipments are deleted. This event-driven approach using onDelete: "NO ACTION" with createForeignKeyConstraints: false allows for sophisticated business logic during cleanup while maintaining referential integrity through application-level event handling.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/document-status.njk (9)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#37
File: apps/portal-api/src/document/queues/file.processor.ts:165-166
Timestamp: 2024-10-25T14:10:02.259Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, within the `spawnDataExtractionJob` method, failing to update the file status with `updateFileStatus` does not require throwing an error, as it only results in the status being displayed differently.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#630
File: apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts:299-304
Timestamp: 2025-06-10T21:28:42.783Z
Learning: In apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts, the CAD payload should treat monetary 0 values the same as null/undefined, rendering them as empty strings (e.g., for valueForCurrencyConversion and valueForDuty).
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#533
File: apps/portal/src/modules/Shipment/components/DutySummaryTab/DutySummaryTab.component.tsx:109-117
Timestamp: 2025-04-30T15:20:29.367Z
Learning: In the DutySummaryTab component, the "Recalculate Duties and Taxes" functionality is intentionally only available after an entry has been submitted, which is why the conditional uses `!canSubmitEntry(shipment.customsStatus)`.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/status-line.njk (10)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/normalized-mapped-fields.njk:25-35
Timestamp: 2025-01-27T21:31:16.677Z
Learning: Validation improvements for the normalized-mapped-fields.njk template, including maximum field length constraints, special character handling/sanitization, and date format validation for ISO 8601, will be implemented in future PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:41:41.610Z
Learning: In Nunjucks template files (.njk) used for LLM prompts, markdown formatting concerns about fenced code blocks don't apply because: 1) the content is processed by the template engine rather than rendered as markdown, and 2) the resulting text is consumed by LLMs which process raw content regardless of markdown syntax highlighting issues.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:102-133
Timestamp: 2025-01-27T22:11:26.710Z
Learning: The email content template in `analyze-email-intent.njk` requires security improvements including variable checks, content sanitization, and encoding handling.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:38:47.489Z
Learning: In Nunjucks template files (.njk), markdown formatting concerns about fenced code blocks don't apply because the content is processed by the template engine rather than rendered as markdown. The template system handles content differently than regular markdown files.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/documentation-coming-messages.njk (3)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#682
File: apps/portal-api/src/email/types/gmail.types.ts:0-0
Timestamp: 2025-07-04T18:37:10.290Z
Learning: The commented-out properties (text, html, attachments) in the ParsedGmailMessage interface in apps/portal-api/src/email/types/gmail.types.ts are intentionally kept and will be removed in later commits by alex-aistribute.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

</details>
<details>
<summary>apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk (8)</summary>

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk:12-19
Timestamp: 2025-05-26T14:29:32.717Z
Learning: In apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk, REQUEST_RNS_PROOF is intentionally omitted from the classifier prompt even though it exists in the EmailIntentType enum. This is a temporary state while the feature is being developed - the enum value is prepared but the classifier shouldn't recognize this intent until the backend processing is fully implemented.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:102-133
Timestamp: 2025-01-27T22:11:26.710Z
Learning: The email content template in `analyze-email-intent.njk` requires security improvements including variable checks, content sanitization, and encoding handling.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#536
File: apps/portal-api/src/core-agent/constants/validate-compliance-response.descriptions.ts:17-73
Timestamp: 2025-05-05T06:26:34.093Z
Learning: Consistency edits for field description prefixes in MISSING_SHIPMENT_FIELD_DESCRIPTIONS (such as adding "Missing" to all fields) were considered unnecessary by the user.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:1-18
Timestamp: 2025-01-27T23:11:25.008Z
Learning: Security validation rules including PII detection, shipment reference validation, and suspicious input handling will be added to the email intent analysis system in future PRs.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts:197-203
Timestamp: 2025-04-16T19:42:07.625Z
Learning: In the email-intent-analysis.service.ts switch statement, explicitly listing case statements that fall through to default is intentional for documentation and future extensibility, even if they appear redundant from a pure execution perspective.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/acknowledge-missing-documents-messages.njk (7)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#536
File: apps/portal-api/src/core-agent/constants/validate-compliance-response.descriptions.ts:17-73
Timestamp: 2025-05-05T06:26:34.093Z
Learning: Consistency edits for field description prefixes in MISSING_SHIPMENT_FIELD_DESCRIPTIONS (such as adding "Missing" to all fields) were considered unnecessary by the user.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:41:41.610Z
Learning: In Nunjucks template files (.njk) used for LLM prompts, markdown formatting concerns about fenced code blocks don't apply because: 1) the content is processed by the template engine rather than rendered as markdown, and 2) the resulting text is consumed by LLMs which process raw content regardless of markdown syntax highlighting issues.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/details-header.njk (1)</summary>

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:38:47.489Z
Learning: In Nunjucks template files (.njk), markdown formatting concerns about fenced code blocks don't apply because the content is processed by the template engine rather than rendered as markdown. The template system handles content differently than regular markdown files.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/acknowledge-documents-messages.njk (6)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:41:41.610Z
Learning: In Nunjucks template files (.njk) used for LLM prompts, markdown formatting concerns about fenced code blocks don't apply because: 1) the content is processed by the template engine rather than rendered as markdown, and 2) the resulting text is consumed by LLMs which process raw content regardless of markdown syntax highlighting issues.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/shipment-identifiers.njk (11)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#438
File: libraries/nest-modules/src/dto/candata-shipment.dto.ts:1173-1200
Timestamp: 2025-03-18T15:27:29.236Z
Learning: The CandataShipmentDto in libraries/nest-modules/src/dto/candata-shipment.dto.ts is specifically used for calling the Candata API and is separate from DTOs in shipment.dto.ts. Changes to field names in CandataShipmentDto (like from cargoControlNumber to cargoControlNumbers) don't conflict with references to similarly named fields in other DTOs.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#324
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:20-22
Timestamp: 2025-01-29T19:35:31.971Z
Learning: The misalignment between prompt requirements (HBL, MBL, BOOKING) and validation schema (HBL, CCN, PAR) in shipment identifier types is known and will be addressed in future PRs.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#97
File: apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx:81-84
Timestamp: 2024-11-12T22:35:26.847Z
Learning: In the file `apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx`, commented-out code may be intentionally kept for future use. Do not suggest removing such commented-out code in this file unless specifically instructed.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#214
File: apps/portal/src/modules/Shipment/components/ShipmentHeader/ShipmentHeader.component.tsx:70-76
Timestamp: 2024-12-18T18:39:26.865Z
Learning: In the ShipmentHeader component (apps/portal/src/modules/Shipment/components/ShipmentHeader/ShipmentHeader.component.tsx), the user has confirmed that the shipment.organization object is always guaranteed to exist, so no null checks are necessary when accessing shipment.organization.name.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: libraries/nest-modules/src/dto/shipment-tracking.dto.ts:7-12
Timestamp: 2024-10-28T22:13:57.081Z
Learning: Enhancing validation and documentation for container numbers in `CarrierShipmentTrackingDto` is not needed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#29
File: libraries/nest-modules/src/dto/shipment.dto.ts:299-304
Timestamp: 2024-10-22T21:56:21.991Z
Learning: In `libraries/nest-modules/src/dto/shipment.dto.ts`, the field `portOfDischargeId` is not related to changes in location types, so adding validation for these types is not applicable.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/extract-shipment-identifiers.njk:8-14
Timestamp: 2025-01-27T21:32:19.876Z
Learning: Shipment identifiers (HBL, CCN, PARS) do not follow fixed patterns and cannot be validated using regex patterns.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/hold-shipment-messages.njk (10)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#97
File: apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx:81-84
Timestamp: 2024-11-12T22:35:26.847Z
Learning: In the file `apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx`, commented-out code may be intentionally kept for future use. Do not suggest removing such commented-out code in this file unless specifically instructed.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:41:41.610Z
Learning: In Nunjucks template files (.njk) used for LLM prompts, markdown formatting concerns about fenced code blocks don't apply because: 1) the content is processed by the template engine rather than rendered as markdown, and 2) the resulting text is consumed by LLMs which process raw content regardless of markdown syntax highlighting issues.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

</details>
<details>
<summary>apps/portal-api/test-classification.js (15)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/email/controllers/email.controller.ts:14-21
Timestamp: 2025-01-27T20:48:53.751Z
Learning: The test endpoint in `apps/portal-api/src/email/controllers/email.controller.ts` is temporary and will be removed in later PRs.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts:197-203
Timestamp: 2025-04-16T19:42:07.625Z
Learning: In the email-intent-analysis.service.ts switch statement, explicitly listing case statements that fall through to default is intentional for documentation and future extensibility, even if they appear redundant from a pure execution perspective.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk:12-19
Timestamp: 2025-05-26T14:29:32.717Z
Learning: In apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk, REQUEST_RNS_PROOF is intentionally omitted from the classifier prompt even though it exists in the EmailIntentType enum. This is a temporary state while the feature is being developed - the enum value is prepared but the classifier shouldn't recognize this intent until the backend processing is fully implemented.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: libraries/nest-modules/src/entities/gmail-token.entity.ts:4-16
Timestamp: 2025-01-27T20:51:08.422Z
Learning: Audit fields (createdAt, updatedAt) and improved email validation for SimplifiedGmailToken class in libraries/nest-modules/src/entities/gmail-token.entity.ts will be addressed in future PRs.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#332
File: apps/portal-api/src/email/email.module.ts:72-133
Timestamp: 2025-02-03T20:04:38.211Z
Learning: The commented-out code in EmailModule (apps/portal-api/src/email/email.module.ts) is planned to be removed in future PRs. This technical debt has been acknowledged and scheduled for cleanup.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:1-18
Timestamp: 2025-01-27T23:11:25.008Z
Learning: Security validation rules including PII detection, shipment reference validation, and suspicious input handling will be added to the email intent analysis system in future PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/email/utils/run-transaction.ts:3-8
Timestamp: 2025-01-27T20:49:25.625Z
Learning: The `runTransaction` function in `apps/portal-api/src/email/utils/run-transaction.ts` is not in use as of January 2025 and will be reviewed later when it's needed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#52
File: apps/portal-api/src/ogd-filing/ogd-filing.module.ts:5-5
Timestamp: 2024-10-29T15:25:16.819Z
Learning: Importing from the 'nest-modules' index file is intended in this project.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/document.module.ts:43-69
Timestamp: 2024-10-16T17:29:38.745Z
Learning: In Nest.js, defining a class with a static `register` method is the standard pattern for declaring dynamic modules.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/document.module.ts:8-11
Timestamp: 2024-10-16T17:30:11.487Z
Learning: In this application, `File` and `Document` are defined within the application and correctly imported from `'nest-modules'`.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#19
File: apps/portal-api/src/document/services/openai/abstract.task.ts:8-10
Timestamp: 2024-10-22T20:19:34.178Z
Learning: In 'apps/portal-api/src/document/services/openai/abstract.task.ts', the 'AbstractTask' class uses 'any' types for the 'params' getter and 'execute' method because different OpenAI calls have different parameters and return types, which are specified in the concrete implementations.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/rush-messages.njk (9)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#472
File: apps/portal-api/src/commercial-invoice/commercial-invoice.service.ts:216-222
Timestamp: 2025-04-08T13:20:07.264Z
Learning: The validation preventing modification of commercial invoices for shipments with customs status ENTRY_SUBMITTED, EXAM, or RELEASED has been intentionally removed. This is because subsequent changes to commercial invoices will now automatically be updated to the entry through the synchronization mechanism implemented in the EntrySubmissionService and related components.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#630
File: apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts:299-304
Timestamp: 2025-06-10T21:28:42.783Z
Learning: In apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts, the CAD payload should treat monetary 0 values the same as null/undefined, rendering them as empty strings (e.g., for valueForCurrencyConversion and valueForDuty).
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk:12-19
Timestamp: 2025-05-26T14:29:32.717Z
Learning: In apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk, REQUEST_RNS_PROOF is intentionally omitted from the classifier prompt even though it exists in the EmailIntentType enum. This is a temporary state while the feature is being developed - the enum value is prepared but the classifier shouldn't recognize this intent until the backend processing is fully implemented.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/normalized-mapped-fields.njk:25-35
Timestamp: 2025-01-27T21:31:16.677Z
Learning: Validation improvements for the normalized-mapped-fields.njk template, including maximum field length constraints, special character handling/sanitization, and date format validation for ISO 8601, will be implemented in future PRs.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/shipment-update-messages.njk (10)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#97
File: apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx:81-84
Timestamp: 2024-11-12T22:35:26.847Z
Learning: In the file `apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx`, commented-out code may be intentionally kept for future use. Do not suggest removing such commented-out code in this file unless specifically instructed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#183
File: libraries/nest-modules/src/types/shipment.types.ts:36-40
Timestamp: 2024-11-28T21:25:15.847Z
Learning: When `WeightUOM` enums `KGS` and `LBS` are deprecated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the usage in other files, such as `apps/portal-api/src/aggregation/parser.ts`, in separate PRs.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#29
File: libraries/nest-modules/src/dto/shipment.dto.ts:299-304
Timestamp: 2024-10-22T21:56:21.991Z
Learning: In `libraries/nest-modules/src/dto/shipment.dto.ts`, the field `portOfDischargeId` is not related to changes in location types, so adding validation for these types is not applicable.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#80
File: libraries/nest-modules/src/candata/candata.service.ts:93-95
Timestamp: 2024-11-06T18:49:10.085Z
Learning: In 'candata.service.ts', the `updateCandataShipment` method will be implemented later. No need to change its current implementation.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#214
File: apps/portal/src/modules/Shipment/components/ShipmentHeader/ShipmentHeader.component.tsx:70-76
Timestamp: 2024-12-18T18:39:26.865Z
Learning: In the ShipmentHeader component (apps/portal/src/modules/Shipment/components/ShipmentHeader/ShipmentHeader.component.tsx), the user has confirmed that the shipment.organization object is always guaranteed to exist, so no null checks are necessary when accessing shipment.organization.name.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/validation-issues.njk (8)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/normalized-mapped-fields.njk:25-35
Timestamp: 2025-01-27T21:31:16.677Z
Learning: Validation improvements for the normalized-mapped-fields.njk template, including maximum field length constraints, special character handling/sanitization, and date format validation for ISO 8601, will be implemented in future PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/structureless-to-json.njk:13-18
Timestamp: 2025-01-27T21:29:51.404Z
Learning: Email content validation for the structureless-to-json.njk template is handled before the values are provided to the template, making template-level validation unnecessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#29
File: libraries/nest-modules/src/dto/shipment.dto.ts:299-304
Timestamp: 2024-10-22T21:56:21.991Z
Learning: In `libraries/nest-modules/src/dto/shipment.dto.ts`, the field `portOfDischargeId` is not related to changes in location types, so adding validation for these types is not applicable.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:102-133
Timestamp: 2025-01-27T22:11:26.710Z
Learning: The email content template in `analyze-email-intent.njk` requires security improvements including variable checks, content sanitization, and encoding handling.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:41:41.610Z
Learning: In Nunjucks template files (.njk) used for LLM prompts, markdown formatting concerns about fenced code blocks don't apply because: 1) the content is processed by the template engine rather than rendered as markdown, and 2) the resulting text is consumed by LLMs which process raw content regardless of markdown syntax highlighting issues.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/email/schemas/email-content.schema.ts:3-9
Timestamp: 2025-01-27T21:27:00.465Z
Learning: The EmailAttachmentSchema in apps/portal-api/src/email/schemas/email-content.schema.ts needs security improvements in future PRs:
1. Replace z.any() with more specific validation
2. Add size limits for filename
3. Add enum validation for documentType
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts (11)</summary>

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts:197-203
Timestamp: 2025-04-16T19:42:07.625Z
Learning: In the email-intent-analysis.service.ts switch statement, explicitly listing case statements that fall through to default is intentional for documentation and future extensibility, even if they appear redundant from a pure execution perspective.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#37
File: apps/portal-api/src/document/queues/file.processor.ts:156-162
Timestamp: 2024-10-25T14:10:39.695Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, worker methods like `updateFileStatus` do not need to propagate the result and can return `void` instead of `boolean` since the worker doesn't need to know the result.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#591
File: apps/portal-api/src/email/listeners/email.listener.ts:257-260
Timestamp: 2025-05-28T14:36:35.614Z
Learning: In the email sync system for apps/portal-api/src/email/listeners/email.listener.ts and apps/portal-api/src/email/processors/get-gmail-message.processor.ts: Partial sync updates the Gmail token's history ID once in the listener using the batch's latestHistoryId from the API, while full sync updates the history ID in the processor using each individual message's historyId. This is intentional design - out-of-order message processing during full sync is acceptable because the next sync will pick up from the final history ID and catch any missed messages.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#332
File: apps/portal-api/src/email/email.module.ts:72-133
Timestamp: 2025-02-03T20:04:38.211Z
Learning: The commented-out code in EmailModule (apps/portal-api/src/email/email.module.ts) is planned to be removed in future PRs. This technical debt has been acknowledged and scheduled for cleanup.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#37
File: apps/portal-api/src/document/queues/file.processor.ts:165-166
Timestamp: 2024-10-25T14:10:02.259Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, within the `spawnDataExtractionJob` method, failing to update the file status with `updateFileStatus` does not require throwing an error, as it only results in the status being displayed differently.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#48
File: apps/portal-api/src/shipment/shipment.service.ts:98-98
Timestamp: 2024-10-28T19:41:18.814Z
Learning: The `editShipment` method does not have `documentIds`, so it does not need to handle plural property names.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/main-messages.njk (9)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:102-133
Timestamp: 2025-01-27T22:11:26.710Z
Learning: The email content template in `analyze-email-intent.njk` requires security improvements including variable checks, content sanitization, and encoding handling.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#682
File: apps/portal-api/src/email/types/gmail.types.ts:0-0
Timestamp: 2025-07-04T18:37:10.290Z
Learning: The commented-out properties (text, html, attachments) in the ParsedGmailMessage interface in apps/portal-api/src/email/types/gmail.types.ts are intentionally kept and will be removed in later commits by alex-aistribute.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#332
File: apps/portal-api/src/email/email.module.ts:72-133
Timestamp: 2025-02-03T20:04:38.211Z
Learning: The commented-out code in EmailModule (apps/portal-api/src/email/email.module.ts) is planned to be removed in future PRs. This technical debt has been acknowledged and scheduled for cleanup.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk:12-19
Timestamp: 2025-05-26T14:29:32.717Z
Learning: In apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk, REQUEST_RNS_PROOF is intentionally omitted from the classifier prompt even though it exists in the EmailIntentType enum. This is a temporary state while the feature is being developed - the enum value is prepared but the classifier shouldn't recognize this intent until the backend processing is fully implemented.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:41:41.610Z
Learning: In Nunjucks template files (.njk) used for LLM prompts, markdown formatting concerns about fenced code blocks don't apply because: 1) the content is processed by the template engine rather than rendered as markdown, and 2) the resulting text is consumed by LLMs which process raw content regardless of markdown syntax highlighting issues.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#536
File: apps/portal-api/src/llm/ask-llm/services/ask-llm.service.ts:585-598
Timestamp: 2025-05-02T13:59:43.977Z
Learning: Synchronous file I/O in the `addImagesToMessages` method of `apps/portal-api/src/llm/ask-llm/services/ask-llm.service.ts` is known and scheduled for future refactoring.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/normalized-mapped-fields.njk:25-35
Timestamp: 2025-01-27T21:31:16.677Z
Learning: Validation improvements for the normalized-mapped-fields.njk template, including maximum field length constraints, special character handling/sanitization, and date format validation for ISO 8601, will be implemented in future PRs.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/rns-messages.njk (10)</summary>

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk:12-19
Timestamp: 2025-05-26T14:29:32.717Z
Learning: In apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk, REQUEST_RNS_PROOF is intentionally omitted from the classifier prompt even though it exists in the EmailIntentType enum. This is a temporary state while the feature is being developed - the enum value is prepared but the classifier shouldn't recognize this intent until the backend processing is fully implemented.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#630
File: apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts:299-304
Timestamp: 2025-06-10T21:28:42.783Z
Learning: In apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts, the CAD payload should treat monetary 0 values the same as null/undefined, rendering them as empty strings (e.g., for valueForCurrencyConversion and valueForDuty).
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:41:41.610Z
Learning: In Nunjucks template files (.njk) used for LLM prompts, markdown formatting concerns about fenced code blocks don't apply because: 1) the content is processed by the template engine rather than rendered as markdown, and 2) the resulting text is consumed by LLMs which process raw content regardless of markdown syntax highlighting issues.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/normalized-mapped-fields.njk:25-35
Timestamp: 2025-01-27T21:31:16.677Z
Learning: Validation improvements for the normalized-mapped-fields.njk template, including maximum field length constraints, special character handling/sanitization, and date format validation for ISO 8601, will be implemented in future PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: libraries/nest-modules/src/types/shipment-tracking.types.ts:56-59
Timestamp: 2024-11-01T11:37:08.671Z
Learning: In `libraries/nest-modules/src/types/shipment-tracking.types.ts`, the `RAILWAY_ACTOR_MAPPINGS` should use development endpoints (`antek~dev-cp-container-status-check` and `antek~dev-cn-container-status-check`) for now.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/types/response-fragment.types.ts (4)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#682
File: apps/portal-api/src/email/types/gmail.types.ts:0-0
Timestamp: 2025-07-04T18:37:10.290Z
Learning: The commented-out properties (text, html, attachments) in the ParsedGmailMessage interface in apps/portal-api/src/email/types/gmail.types.ts are intentionally kept and will be removed in later commits by alex-aistribute.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/shipment-update-error-messages.njk (11)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#97
File: apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx:81-84
Timestamp: 2024-11-12T22:35:26.847Z
Learning: In the file `apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx`, commented-out code may be intentionally kept for future use. Do not suggest removing such commented-out code in this file unless specifically instructed.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#29
File: libraries/nest-modules/src/dto/shipment.dto.ts:299-304
Timestamp: 2024-10-22T21:56:21.991Z
Learning: In `libraries/nest-modules/src/dto/shipment.dto.ts`, the field `portOfDischargeId` is not related to changes in location types, so adding validation for these types is not applicable.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#183
File: libraries/nest-modules/src/types/shipment.types.ts:36-40
Timestamp: 2024-11-28T21:25:15.847Z
Learning: When `WeightUOM` enums `KGS` and `LBS` are deprecated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the usage in other files, such as `apps/portal-api/src/aggregation/parser.ts`, in separate PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: libraries/nest-modules/src/candata/candata.service.ts:94-94
Timestamp: 2024-11-21T22:47:35.289Z
Learning: In the `createCandataShipment` method of `CandataService` in `libraries/nest-modules/src/candata/candata.service.ts`, the error response from the Candata API uses `"error message"` as the key for the error message instead of `"message"`.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/testing/e2e-email-pipeline-nestjs.js (10)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#682
File: apps/portal-api/src/email/types/gmail.types.ts:0-0
Timestamp: 2025-07-04T18:37:10.290Z
Learning: The commented-out properties (text, html, attachments) in the ParsedGmailMessage interface in apps/portal-api/src/email/types/gmail.types.ts are intentionally kept and will be removed in later commits by alex-aistribute.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/email/schemas/email-content.schema.ts:3-9
Timestamp: 2025-01-27T21:27:00.465Z
Learning: The EmailAttachmentSchema in apps/portal-api/src/email/schemas/email-content.schema.ts needs security improvements in future PRs:
1. Replace z.any() with more specific validation
2. Add size limits for filename
3. Add enum validation for documentType
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/email/controllers/email.controller.ts:14-21
Timestamp: 2025-01-27T20:48:53.751Z
Learning: The test endpoint in `apps/portal-api/src/email/controllers/email.controller.ts` is temporary and will be removed in later PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#332
File: apps/portal-api/src/email/email.module.ts:72-133
Timestamp: 2025-02-03T20:04:38.211Z
Learning: The commented-out code in EmailModule (apps/portal-api/src/email/email.module.ts) is planned to be removed in future PRs. This technical debt has been acknowledged and scheduled for cleanup.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/email/types/gmail.types.ts:36-49
Timestamp: 2025-01-27T21:27:57.041Z
Learning: The attachment properties (fileName, mimeType, buffer) in the `ParsedGmailMessage` interface will be made non-nullable in future PRs to enhance type safety.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#640
File: apps/portal-api/src/email/email.module.ts:80-93
Timestamp: 2025-06-16T17:23:42.307Z
Learning: The pattern of using `process.env.FEATURE` directly (without explicit boolean conversion) to control processor inclusion is intentionally implemented across multiple modules including EmailModule and OgdFilingModule. Empty string values being falsy and enabling processors is the desired behavior confirmed by alex-aistribute.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: libraries/nest-modules/src/entities/gmail-token.entity.ts:4-16
Timestamp: 2025-01-27T20:51:08.422Z
Learning: Audit fields (createdAt, updatedAt) and improved email validation for SimplifiedGmailToken class in libraries/nest-modules/src/entities/gmail-token.entity.ts will be addressed in future PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/email/processors/get-gmail-message.processor.ts:193-218
Timestamp: 2025-01-27T16:32:50.279Z
Learning: In the GetGmailMessageProcessor, the EMAIL_SAVED event should only be emitted when the email has no attachments. When attachments exist, the event emission is handled elsewhere after attachment processing.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/manual-processing-messages.njk (10)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:102-133
Timestamp: 2025-01-27T22:11:26.710Z
Learning: The email content template in `analyze-email-intent.njk` requires security improvements including variable checks, content sanitization, and encoding handling.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#682
File: apps/portal-api/src/email/types/gmail.types.ts:0-0
Timestamp: 2025-07-04T18:37:10.290Z
Learning: The commented-out properties (text, html, attachments) in the ParsedGmailMessage interface in apps/portal-api/src/email/types/gmail.types.ts are intentionally kept and will be removed in later commits by alex-aistribute.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:41:41.610Z
Learning: In Nunjucks template files (.njk) used for LLM prompts, markdown formatting concerns about fenced code blocks don't apply because: 1) the content is processed by the template engine rather than rendered as markdown, and 2) the resulting text is consumed by LLMs which process raw content regardless of markdown syntax highlighting issues.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/normalized-mapped-fields.njk:25-35
Timestamp: 2025-01-27T21:31:16.677Z
Learning: Validation improvements for the normalized-mapped-fields.njk template, including maximum field length constraints, special character handling/sanitization, and date format validation for ISO 8601, will be implemented in future PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#332
File: apps/portal-api/src/email/email.module.ts:72-133
Timestamp: 2025-02-03T20:04:38.211Z
Learning: The commented-out code in EmailModule (apps/portal-api/src/email/email.module.ts) is planned to be removed in future PRs. This technical debt has been acknowledged and scheduled for cleanup.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk:12-19
Timestamp: 2025-05-26T14:29:32.717Z
Learning: In apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk, REQUEST_RNS_PROOF is intentionally omitted from the classifier prompt even though it exists in the EmailIntentType enum. This is a temporary state while the feature is being developed - the enum value is prepared but the classifier shouldn't recognize this intent until the backend processing is fully implemented.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:38:47.489Z
Learning: In Nunjucks template files (.njk), markdown formatting concerns about fenced code blocks don't apply because the content is processed by the template engine rather than rendered as markdown. The template system handles content differently than regular markdown files.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/services/shipment-response.service.ts (10)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#58
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:122-127
Timestamp: 2024-10-30T18:27:11.584Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, within the `carrierShipmentTracking` method, the code verifies the existence of shipments before creating tracking history, so additional checks are unnecessary.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:247-314
Timestamp: 2024-11-01T11:53:56.057Z
Learning: The team prefers to keep the existing implementation of the railway shipment processing logic in the `_fetchAndUpdateRailwayShipments` method in `shipment-tracking.service.ts` without further refactoring.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: apps/portal-api/src/shipment/shipment.service.ts:12-12
Timestamp: 2024-11-13T21:23:02.422Z
Learning: In `shipment.service.ts`, the import statement for `RuleQueryService` from `node_modules` has been fixed in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:99-114
Timestamp: 2024-10-28T22:15:53.070Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts', the code already filters the containers and handles null values when `etaPort` cannot be extracted, so additional error handling is not necessary.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/document-processing-messages.njk (10)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:41:41.610Z
Learning: In Nunjucks template files (.njk) used for LLM prompts, markdown formatting concerns about fenced code blocks don't apply because: 1) the content is processed by the template engine rather than rendered as markdown, and 2) the resulting text is consumed by LLMs which process raw content regardless of markdown syntax highlighting issues.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#19
File: apps/portal-api/src/document/queues/document.processor.ts:72-74
Timestamp: 2024-10-22T19:06:49.145Z
Learning: In the 'DocumentProcessor' class in `apps/portal-api/src/document/queues/document.processor.ts`, errors are expected to be discarded, so rethrowing or wrapping the original error is not necessary.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/identify-shipment-prompt.njk:50-63
Timestamp: 2025-05-26T16:38:47.489Z
Learning: In Nunjucks template files (.njk), markdown formatting concerns about fenced code blocks don't apply because the content is processed by the template engine rather than rendered as markdown. The template system handles content differently than regular markdown files.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/cad-messages.njk (10)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#630
File: apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts:299-304
Timestamp: 2025-06-10T21:28:42.783Z
Learning: In apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts, the CAD payload should treat monetary 0 values the same as null/undefined, rendering them as empty strings (e.g., for valueForCurrencyConversion and valueForDuty).
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#438
File: libraries/nest-modules/src/dto/candata-shipment.dto.ts:1173-1200
Timestamp: 2025-03-18T15:27:29.236Z
Learning: The CandataShipmentDto in libraries/nest-modules/src/dto/candata-shipment.dto.ts is specifically used for calling the Candata API and is separate from DTOs in shipment.dto.ts. Changes to field names in CandataShipmentDto (like from cargoControlNumber to cargoControlNumbers) don't conflict with references to similarly named fields in other DTOs.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#472
File: apps/portal-api/src/commercial-invoice/commercial-invoice.service.ts:216-222
Timestamp: 2025-04-08T13:20:07.264Z
Learning: The validation preventing modification of commercial invoices for shipments with customs status ENTRY_SUBMITTED, EXAM, or RELEASED has been intentionally removed. This is because subsequent changes to commercial invoices will now automatically be updated to the entry through the synchronization mechanism implemented in the EntrySubmissionService and related components.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#126
File: libraries/nest-modules/src/canada-tariff/canada-tariff.service.ts:256-258
Timestamp: 2024-11-19T18:49:07.696Z
Learning: In `libraries/nest-modules/src/canada-tariff/canada-tariff.service.ts`, it's acceptable to store raw error messages in the `TariffSyncHistory` entity since they are not exposed to outside users.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#174
File: apps/portal-api/src/shipment/shipment.service.ts:517-523
Timestamp: 2024-11-27T15:29:18.862Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, if `relatedCanadaTariffs` is empty, it is intentional that all invoice lines are considered to have invalid HS codes.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts (37)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#538
File: apps/portal-api/src/core-agent/answers/shipping-status.answer.ts:14-19
Timestamp: 2025-05-05T10:35:07.459Z
Learning: For shipping-status.answer.ts, the developer prefers a simpler implementation using basic string literals and direct array includes checks rather than extracted constants and more complex matching logic.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:247-314
Timestamp: 2024-11-01T11:53:56.057Z
Learning: The team prefers to keep the existing implementation of the railway shipment processing logic in the `_fetchAndUpdateRailwayShipments` method in `shipment-tracking.service.ts` without further refactoring.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:99-114
Timestamp: 2024-10-28T22:15:53.070Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts', the code already filters the containers and handles null values when `etaPort` cannot be extracted, so additional error handling is not necessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#58
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:122-127
Timestamp: 2024-10-30T18:27:11.584Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, within the `carrierShipmentTracking` method, the code verifies the existence of shipments before creating tracking history, so additional checks are unnecessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/core-agent.service.ts:248-321
Timestamp: 2025-04-16T19:48:08.355Z
Learning: The CoreAgentService.answerShipmentQuestions method is intentionally kept as a single method without further abstraction to maintain directness and clarity, even though it orchestrates multiple operations (shipment fetch, compliance fetch, answer generation).
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#115
File: apps/portal-api/src/shipment/shipment.controller.ts:86-88
Timestamp: 2024-11-15T21:29:14.787Z
Learning: Error handling for the `getShipmentCustomsActivities` method is implemented within the `getShipmentCustomsActivities` function inside `ShipmentService` (file `shipment.service.ts`), so explicit error handling in `ShipmentController` (file `shipment.controller.ts`) is not necessary.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:199-199
Timestamp: 2024-11-06T19:18:12.548Z
Learning: In the `ShipmentLinker` class, the `tradePartnerPromise()` and `locationPromise()` functions each only modify their own arrays (`pendingTradePartners` and `pendingLocations`), so they can be safely executed concurrently using `Promise.all()`.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: apps/portal-api/src/shipment/shipment.controller.ts:62-64
Timestamp: 2024-11-13T21:00:27.986Z
Learning: In the NestJS TypeScript file `shipment.controller.ts`, for the `ShipmentController`'s `validateShipmentCompliance()` method, explicit error handling in the controller is not required because error handling is performed within `validateShipmentCompliance()` in `ShipmentService`.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:204-311
Timestamp: 2024-11-01T12:10:48.324Z
Learning: In the `_fetchCNOrCPContainerStatus` method in `shipment-tracking.service.ts`, processing a large number of container numbers may cause requests to the Apify API to take longer to complete.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:208-226
Timestamp: 2024-11-01T11:34:22.122Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, when handling async Axios calls in TypeScript, it's acceptable to handle errors by chaining `.catch()` methods instead of using `try...catch` blocks.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: libraries/nest-modules/src/candata/candata.service.ts:67-67
Timestamp: 2024-11-21T22:46:30.005Z
Learning: In `libraries/nest-modules/src/candata/candata.service.ts`, it's acceptable to log the full error object using `this.logger.error(error);` because the error logs are not available to general users.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#14
File: apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx:25-25
Timestamp: 2024-10-16T21:26:11.352Z
Learning: In `apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx`, error handling is managed in the service layer, so additional error handling in the component is unnecessary.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#622
File: apps/portal/src/modules/Shipment/queries/GetDutySummary.ts:35-40
Timestamp: 2025-06-06T21:33:03.394Z
Learning: When fetching exchange rates in GetDutySummary.ts, if the ShipmentService.ExchangeRate.get API call fails, the error should be ignored and the exchange rate should default to 1.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: libraries/nest-modules/src/types/shipment.types.ts:54-61
Timestamp: 2024-11-13T21:01:38.354Z
Learning: When backend `CustomsStatus` enums are updated in `libraries/nest-modules/src/types/shipment.types.ts`, it's acceptable to update the corresponding frontend enums in separate PRs.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#107
File: apps/frontend/src/modules/Shipment/Utils.shipment.ts:61-62
Timestamp: 2024-11-14T15:29:33.567Z
Learning: In `apps/frontend/src/modules/Shipment/Utils.shipment.ts`, when defining custom label colors in the `customsLabelColor` function, it's acceptable to add new color schemes beyond the standard `StatusLabel` component color schemes from the design system when necessary.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#114
File: apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx:82-85
Timestamp: 2024-11-15T21:14:36.145Z
Learning: In `apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx`, adding a null check for the `shipment` prop before rendering `DutySummaryTab` is optional.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#97
File: apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx:81-84
Timestamp: 2024-11-12T22:35:26.847Z
Learning: In the file `apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx`, commented-out code may be intentionally kept for future use. Do not suggest removing such commented-out code in this file unless specifically instructed.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#114
File: apps/frontend/src/modules/Shipment/components/DutySummaryTab/DutySummaryTab.component.tsx:10-10
Timestamp: 2024-11-15T21:13:50.826Z
Learning: In `apps/frontend/src/modules/Shipment/components/DutySummaryTab/DutySummaryTab.component.tsx`, the `shipment` prop is already defined as required. Future code reviews should not suggest changing its type definition unless there are other considerations.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:234-237
Timestamp: 2024-10-28T14:39:41.336Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, the `getRailShipmentTracking` method is intentionally left as a placeholder for future implementation and should be retained even if it's currently unused.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#538
File: apps/portal-api/src/core-agent/context-builders/shipment-identifiers.context.ts:9-18
Timestamp: 2025-05-05T10:36:07.755Z
Learning: Context builder functions like `buildShipmentIdentifiersContext` always have a shipment present when called, as this is a design invariant of the system. Defensive null checks for the shipment parameter are unnecessary in these functions.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:715-729
Timestamp: 2024-11-21T23:01:31.477Z
Learning: The `submitShipmentEntry` method in `apps/portal-api/src/shipment/shipment.service.ts` already includes checks for missing Candata products before creating the shipment.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:546-555
Timestamp: 2024-11-21T23:04:32.892Z
Learning: In the `submitShipmentEntry` method of `ShipmentService` in `apps/portal-api/src/shipment/shipment.service.ts`, an infinite loop when generating unique product codes is not possible because the number of Candata products is finite.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#70
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:205-216
Timestamp: 2024-11-05T22:41:32.491Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, it's acceptable to perform shipment updates and tracking history creation without wrapping them in a transaction.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:276-276
Timestamp: 2024-11-04T00:52:23.923Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, within the `_fetchAndUpdateRailwayShipments` method, when accessing `cleanShipments.find(shipment => shipment.containerNumber === recoverContainerNumber)?.id`, the shipment is always found in the `cleanShipments` array, so adding a null check is unnecessary.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#536
File: apps/portal-api/src/core-agent/constants/validate-compliance-response.descriptions.ts:17-73
Timestamp: 2025-05-05T06:26:34.093Z
Learning: Consistency edits for field description prefixes in MISSING_SHIPMENT_FIELD_DESCRIPTIONS (such as adding "Missing" to all fields) were considered unnecessary by the user.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#630
File: apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts:299-304
Timestamp: 2025-06-10T21:28:42.783Z
Learning: In apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts, the CAD payload should treat monetary 0 values the same as null/undefined, rendering them as empty strings (e.g., for valueForCurrencyConversion and valueForDuty).
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#536
File: apps/portal-api/src/core-agent/constants/validate-compliance-response.descriptions.ts:109-127
Timestamp: 2025-05-05T06:27:30.473Z
Learning: In MISSING_FIELD_DESCRIPTIONS constants, only business/compliance-related fields should have the "Missing" prefix. System metadata fields (like id, createDate, lastEditDate, organizationId, etc.) intentionally don't have this prefix.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/acknowledge-missing-documents.handler.ts (13)</summary>

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#19
File: apps/portal-api/src/document/queues/document.processor.ts:72-74
Timestamp: 2024-10-22T19:06:49.145Z
Learning: In the 'DocumentProcessor' class in `apps/portal-api/src/document/queues/document.processor.ts`, errors are expected to be discarded, so rethrowing or wrapping the original error is not necessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:99-114
Timestamp: 2024-10-28T22:15:53.070Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts', the code already filters the containers and handles null values when `etaPort` cannot be extracted, so additional error handling is not necessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#37
File: apps/portal-api/src/document/queues/file.processor.ts:165-166
Timestamp: 2024-10-25T14:10:02.259Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, within the `spawnDataExtractionJob` method, failing to update the file status with `updateFileStatus` does not require throwing an error, as it only results in the status being displayed differently.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#37
File: apps/portal-api/src/document/queues/file.processor.ts:156-162
Timestamp: 2024-10-25T14:10:39.695Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, worker methods like `updateFileStatus` do not need to propagate the result and can return `void` instead of `boolean` since the worker doesn't need to know the result.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/core-agent.service.ts:248-321
Timestamp: 2025-04-16T19:48:08.355Z
Learning: The CoreAgentService.answerShipmentQuestions method is intentionally kept as a single method without further abstraction to maintain directness and clarity, even though it orchestrates multiple operations (shipment fetch, compliance fetch, answer generation).
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/acknowledge-documents.handler.ts (11)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#19
File: apps/portal-api/src/document/queues/document.processor.ts:72-74
Timestamp: 2024-10-22T19:06:49.145Z
Learning: In the 'DocumentProcessor' class in `apps/portal-api/src/document/queues/document.processor.ts`, errors are expected to be discarded, so rethrowing or wrapping the original error is not necessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#37
File: apps/portal-api/src/document/queues/file.processor.ts:156-162
Timestamp: 2024-10-25T14:10:39.695Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, worker methods like `updateFileStatus` do not need to propagate the result and can return `void` instead of `boolean` since the worker doesn't need to know the result.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/core-agent.service.ts:248-321
Timestamp: 2025-04-16T19:48:08.355Z
Learning: The CoreAgentService.answerShipmentQuestions method is intentionally kept as a single method without further abstraction to maintain directness and clarity, even though it orchestrates multiple operations (shipment fetch, compliance fetch, answer generation).
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts (16)</summary>

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts:197-203
Timestamp: 2025-04-16T19:42:07.625Z
Learning: In the email-intent-analysis.service.ts switch statement, explicitly listing case statements that fall through to default is intentional for documentation and future extensibility, even if they appear redundant from a pure execution perspective.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:1-18
Timestamp: 2025-01-27T23:11:25.008Z
Learning: Security validation rules including PII detection, shipment reference validation, and suspicious input handling will be added to the email intent analysis system in future PRs.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk:12-19
Timestamp: 2025-05-26T14:29:32.717Z
Learning: In apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk, REQUEST_RNS_PROOF is intentionally omitted from the classifier prompt even though it exists in the EmailIntentType enum. This is a temporary state while the feature is being developed - the enum value is prepared but the classifier shouldn't recognize this intent until the backend processing is fully implemented.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#313
File: apps/portal-api/src/llm/prompt-templates/analyze-email-intent.njk:102-133
Timestamp: 2025-01-27T22:11:26.710Z
Learning: The email content template in `analyze-email-intent.njk` requires security improvements including variable checks, content sanitization, and encoding handling.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#332
File: apps/portal-api/src/email/email.module.ts:72-133
Timestamp: 2025-02-03T20:04:38.211Z
Learning: The commented-out code in EmailModule (apps/portal-api/src/email/email.module.ts) is planned to be removed in future PRs. This technical debt has been acknowledged and scheduled for cleanup.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#682
File: apps/portal-api/src/email/types/gmail.types.ts:0-0
Timestamp: 2025-07-04T18:37:10.290Z
Learning: The commented-out properties (text, html, attachments) in the ParsedGmailMessage interface in apps/portal-api/src/email/types/gmail.types.ts are intentionally kept and will be removed in later commits by alex-aistribute.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#612
File: libraries/nest-modules/src/global-search/global-search.controller.ts:23-24
Timestamp: 2025-06-04T20:53:34.268Z
Learning: In NestJS applications, when services already handle errors and throw appropriate HTTP exceptions (like InternalServerErrorException), additional error handling in controllers is unnecessary as the framework automatically handles service-thrown exceptions.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#57
File: libraries/nest-modules/src/canada-ogd/canada-ogd.service.ts:19-20
Timestamp: 2024-10-30T17:19:06.971Z
Learning: In this project, always include the `@Inject` decorator when injecting services in NestJS files, even when injecting by class type.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/importer/importer.service.ts:386-387
Timestamp: 2024-11-21T22:52:33.234Z
Learning: In the NestJS `ImporterService` (file `apps/portal-api/src/importer/importer.service.ts`), error logs are not accessible to general users, so logging `error.message` is acceptable.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#536
File: apps/portal-api/src/llm/llm.module.ts:22-56
Timestamp: 2025-05-05T08:28:54.613Z
Learning: DeepSeek is intentionally not exposed through the LLM provider array in apps/portal-api/src/llm/llm.module.ts until its functionality is fully validated, though it's still registered as a service.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#536
File: apps/portal-api/src/llm/openai-enhanced.service.ts:1-18
Timestamp: 2025-05-05T06:22:46.426Z
Learning: In NestJS, when a service extends another service that has dependencies injected in its constructor, the child service inherits those dependencies if they're marked as protected. This allows subclasses to access the parent's injected services without having to redeclare the constructor and re-inject those dependencies.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts (13)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:247-314
Timestamp: 2024-11-01T11:53:56.057Z
Learning: The team prefers to keep the existing implementation of the railway shipment processing logic in the `_fetchAndUpdateRailwayShipments` method in `shipment-tracking.service.ts` without further refactoring.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:99-114
Timestamp: 2024-10-28T22:15:53.070Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts', the code already filters the containers and handles null values when `etaPort` cannot be extracted, so additional error handling is not necessary.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#37
File: apps/portal-api/src/document/queues/file.processor.ts:156-162
Timestamp: 2024-10-25T14:10:39.695Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, worker methods like `updateFileStatus` do not need to propagate the result and can return `void` instead of `boolean` since the worker doesn't need to know the result.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:199-199
Timestamp: 2024-11-06T19:18:12.548Z
Learning: In the `ShipmentLinker` class, the `tradePartnerPromise()` and `locationPromise()` functions each only modify their own arrays (`pendingTradePartners` and `pendingLocations`), so they can be safely executed concurrently using `Promise.all()`.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#70
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:205-216
Timestamp: 2024-11-05T22:41:32.491Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, it's acceptable to perform shipment updates and tracking history creation without wrapping them in a transaction.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#174
File: apps/portal-api/src/shipment/shipment.service.ts:517-523
Timestamp: 2024-11-27T15:29:18.862Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, if `relatedCanadaTariffs` is empty, it is intentional that all invoice lines are considered to have invalid HS codes.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-manual-processing.handler.ts (15)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:247-314
Timestamp: 2024-11-01T11:53:56.057Z
Learning: The team prefers to keep the existing implementation of the railway shipment processing logic in the `_fetchAndUpdateRailwayShipments` method in `shipment-tracking.service.ts` without further refactoring.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#332
File: apps/portal-api/src/email/email.module.ts:72-133
Timestamp: 2025-02-03T20:04:38.211Z
Learning: The commented-out code in EmailModule (apps/portal-api/src/email/email.module.ts) is planned to be removed in future PRs. This technical debt has been acknowledged and scheduled for cleanup.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:99-114
Timestamp: 2024-10-28T22:15:53.070Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts', the code already filters the containers and handles null values when `etaPort` cannot be extracted, so additional error handling is not necessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:199-199
Timestamp: 2024-11-06T19:18:12.548Z
Learning: In the `ShipmentLinker` class, the `tradePartnerPromise()` and `locationPromise()` functions each only modify their own arrays (`pendingTradePartners` and `pendingLocations`), so they can be safely executed concurrently using `Promise.all()`.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#70
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:205-216
Timestamp: 2024-11-05T22:41:32.491Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, it's acceptable to perform shipment updates and tracking history creation without wrapping them in a transaction.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#14
File: apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx:25-25
Timestamp: 2024-10-16T21:26:11.352Z
Learning: In `apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx`, error handling is managed in the service layer, so additional error handling in the component is unnecessary.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:208-226
Timestamp: 2024-11-01T11:34:22.122Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, when handling async Axios calls in TypeScript, it's acceptable to handle errors by chaining `.catch()` methods instead of using `try...catch` blocks.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts (15)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#97
File: apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx:81-84
Timestamp: 2024-11-12T22:35:26.847Z
Learning: In the file `apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx`, commented-out code may be intentionally kept for future use. Do not suggest removing such commented-out code in this file unless specifically instructed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:99-114
Timestamp: 2024-10-28T22:15:53.070Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts', the code already filters the containers and handles null values when `etaPort` cannot be extracted, so additional error handling is not necessary.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/core-agent.service.ts:248-321
Timestamp: 2025-04-16T19:48:08.355Z
Learning: The CoreAgentService.answerShipmentQuestions method is intentionally kept as a single method without further abstraction to maintain directness and clarity, even though it orchestrates multiple operations (shipment fetch, compliance fetch, answer generation).
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#14
File: apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx:25-25
Timestamp: 2024-10-16T21:26:11.352Z
Learning: In `apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx`, error handling is managed in the service layer, so additional error handling in the component is unnecessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: apps/portal-api/src/shipment/shipment.controller.ts:62-64
Timestamp: 2024-11-13T21:00:27.986Z
Learning: In the NestJS TypeScript file `shipment.controller.ts`, for the `ShipmentController`'s `validateShipmentCompliance()` method, explicit error handling in the controller is not required because error handling is performed within `validateShipmentCompliance()` in `ShipmentService`.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:199-199
Timestamp: 2024-11-06T19:18:12.548Z
Learning: In the `ShipmentLinker` class, the `tradePartnerPromise()` and `locationPromise()` functions each only modify their own arrays (`pendingTradePartners` and `pendingLocations`), so they can be safely executed concurrently using `Promise.all()`.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-hold-shipment.handler.ts (15)</summary>

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:247-314
Timestamp: 2024-11-01T11:53:56.057Z
Learning: The team prefers to keep the existing implementation of the railway shipment processing logic in the `_fetchAndUpdateRailwayShipments` method in `shipment-tracking.service.ts` without further refactoring.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:99-114
Timestamp: 2024-10-28T22:15:53.070Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts', the code already filters the containers and handles null values when `etaPort` cannot be extracted, so additional error handling is not necessary.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#97
File: apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx:81-84
Timestamp: 2024-11-12T22:35:26.847Z
Learning: In the file `apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx`, commented-out code may be intentionally kept for future use. Do not suggest removing such commented-out code in this file unless specifically instructed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#58
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:122-127
Timestamp: 2024-10-30T18:27:11.584Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, within the `carrierShipmentTracking` method, the code verifies the existence of shipments before creating tracking history, so additional checks are unnecessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:199-199
Timestamp: 2024-11-06T19:18:12.548Z
Learning: In the `ShipmentLinker` class, the `tradePartnerPromise()` and `locationPromise()` functions each only modify their own arrays (`pendingTradePartners` and `pendingLocations`), so they can be safely executed concurrently using `Promise.all()`.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/core-agent.service.ts:248-321
Timestamp: 2025-04-16T19:48:08.355Z
Learning: The CoreAgentService.answerShipmentQuestions method is intentionally kept as a single method without further abstraction to maintain directness and clarity, even though it orchestrates multiple operations (shipment fetch, compliance fetch, answer generation).
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#538
File: apps/portal-api/src/core-agent/context-builders/shipment-identifiers.context.ts:9-18
Timestamp: 2025-05-05T10:36:07.755Z
Learning: Context builder functions like `buildShipmentIdentifiersContext` always have a shipment present when called, as this is a design invariant of the system. Defensive null checks for the shipment parameter are unnecessary in these functions.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:234-237
Timestamp: 2024-10-28T14:39:41.336Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, the `getRailShipmentTracking` method is intentionally left as a placeholder for future implementation and should be retained even if it's currently unused.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/update-shipment.handler.ts (19)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:247-314
Timestamp: 2024-11-01T11:53:56.057Z
Learning: The team prefers to keep the existing implementation of the railway shipment processing logic in the `_fetchAndUpdateRailwayShipments` method in `shipment-tracking.service.ts` without further refactoring.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:99-114
Timestamp: 2024-10-28T22:15:53.070Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts', the code already filters the containers and handles null values when `etaPort` cannot be extracted, so additional error handling is not necessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#70
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:205-216
Timestamp: 2024-11-05T22:41:32.491Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, it's acceptable to perform shipment updates and tracking history creation without wrapping them in a transaction.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#80
File: libraries/nest-modules/src/candata/candata.service.ts:93-95
Timestamp: 2024-11-06T18:49:10.085Z
Learning: In 'candata.service.ts', the `updateCandataShipment` method will be implemented later. No need to change its current implementation.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:199-199
Timestamp: 2024-11-06T19:18:12.548Z
Learning: In the `ShipmentLinker` class, the `tradePartnerPromise()` and `locationPromise()` functions each only modify their own arrays (`pendingTradePartners` and `pendingLocations`), so they can be safely executed concurrently using `Promise.all()`.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#14
File: apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx:25-25
Timestamp: 2024-10-16T21:26:11.352Z
Learning: In `apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx`, error handling is managed in the service layer, so additional error handling in the component is unnecessary.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:208-226
Timestamp: 2024-11-01T11:34:22.122Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, when handling async Axios calls in TypeScript, it's acceptable to handle errors by chaining `.catch()` methods instead of using `try...catch` blocks.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/core-agent.service.ts:248-321
Timestamp: 2025-04-16T19:48:08.355Z
Learning: The CoreAgentService.answerShipmentQuestions method is intentionally kept as a single method without further abstraction to maintain directness and clarity, even though it orchestrates multiple operations (shipment fetch, compliance fetch, answer generation).
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#538
File: apps/portal-api/src/core-agent/context-builders/shipment-identifiers.context.ts:9-18
Timestamp: 2025-05-05T10:36:07.755Z
Learning: Context builder functions like `buildShipmentIdentifiersContext` always have a shipment present when called, as this is a design invariant of the system. Defensive null checks for the shipment parameter are unnecessary in these functions.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#80
File: libraries/nest-modules/src/candata/candata.service.ts:93-96
Timestamp: 2024-11-06T15:20:06.686Z
Learning: In `libraries/nest-modules/src/candata/candata.service.ts`, the `updateCandataShipment` method is intentionally not implemented because the external Candata API is not ready yet. It will be implemented once Candata provides a workable API.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: apps/portal-api/src/shipment/shipment.controller.ts:62-64
Timestamp: 2024-11-13T21:00:27.986Z
Learning: In the NestJS TypeScript file `shipment.controller.ts`, for the `ShipmentController`'s `validateShipmentCompliance()` method, explicit error handling in the controller is not required because error handling is performed within `validateShipmentCompliance()` in `ShipmentService`.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/base-intent-handler.ts (11)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#465
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:119-135
Timestamp: 2025-04-01T13:58:45.370Z
Learning: The hardcoded timezone "America/Toronto" and portal URL "https://portal-dev.clarocustoms.com/document/" in generate-email-response.processor.ts will be moved to configuration variables in a future PR.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/commercial-invoice/commercial-invoice.service.ts:96-102
Timestamp: 2024-11-21T23:06:15.707Z
Learning: In `apps/portal-api/src/commercial-invoice/commercial-invoice.service.ts`, helper functions are not necessary for refactoring validation logic in the `createCommercialInvoice`, `editCommercialInvoice`, and `deleteCommercialInvoice` methods.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#174
File: apps/portal-api/src/shipment/shipment.service.ts:517-523
Timestamp: 2024-11-27T15:29:18.862Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, if `relatedCanadaTariffs` is empty, it is intentional that all invoice lines are considered to have invalid HS codes.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-rns-proof.handler.ts (16)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:247-314
Timestamp: 2024-11-01T11:53:56.057Z
Learning: The team prefers to keep the existing implementation of the railway shipment processing logic in the `_fetchAndUpdateRailwayShipments` method in `shipment-tracking.service.ts` without further refactoring.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:99-114
Timestamp: 2024-10-28T22:15:53.070Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts', the code already filters the containers and handles null values when `etaPort` cannot be extracted, so additional error handling is not necessary.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#35
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts:23-28
Timestamp: 2024-10-28T13:58:50.237Z
Learning: In 'apps/backoffice-api/src/shipment-tracking/shipment-tracking.controller.ts', the commented port tracking endpoint and the corresponding service methods are planned for implementation in the next PR, so they should be retained.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: apps/portal-api/src/shipment/shipment.service.ts:12-12
Timestamp: 2024-11-13T21:23:02.422Z
Learning: In `shipment.service.ts`, the import statement for `RuleQueryService` from `node_modules` has been fixed in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#630
File: apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts:299-304
Timestamp: 2025-06-10T21:28:42.783Z
Learning: In apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts, the CAD payload should treat monetary 0 values the same as null/undefined, rendering them as empty strings (e.g., for valueForCurrencyConversion and valueForDuty).
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#58
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:122-127
Timestamp: 2024-10-30T18:27:11.584Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, within the `carrierShipmentTracking` method, the code verifies the existence of shipments before creating tracking history, so additional checks are unnecessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk:12-19
Timestamp: 2025-05-26T14:29:32.717Z
Learning: In apps/portal-api/src/llm/prompt-templates/classify-intent-only.njk, REQUEST_RNS_PROOF is intentionally omitted from the classifier prompt even though it exists in the EmailIntentType enum. This is a temporary state while the feature is being developed - the enum value is prepared but the classifier shouldn't recognize this intent until the backend processing is fully implemented.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/core-agent.service.ts:248-321
Timestamp: 2025-04-16T19:48:08.355Z
Learning: The CoreAgentService.answerShipmentQuestions method is intentionally kept as a single method without further abstraction to maintain directness and clarity, even though it orchestrates multiple operations (shipment fetch, compliance fetch, answer generation).
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#102
File: apps/portal-api/src/shipment/shipment.controller.ts:62-64
Timestamp: 2024-11-13T21:00:27.986Z
Learning: In the NestJS TypeScript file `shipment.controller.ts`, for the `ShipmentController`'s `validateShipmentCompliance()` method, explicit error handling in the controller is not required because error handling is performed within `validateShipmentCompliance()` in `ShipmentService`.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:199-199
Timestamp: 2024-11-06T19:18:12.548Z
Learning: In the `ShipmentLinker` class, the `tradePartnerPromise()` and `locationPromise()` functions each only modify their own arrays (`pendingTradePartners` and `pendingLocations`), so they can be safely executed concurrently using `Promise.all()`.
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts (11)</summary>

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#630
File: apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts:299-304
Timestamp: 2025-06-10T21:28:42.783Z
Learning: In apps/portal-api/src/shipment/senders/rns-status-change-email.sender.ts, the CAD payload should treat monetary 0 values the same as null/undefined, rendering them as empty strings (e.g., for valueForCurrencyConversion and valueForDuty).
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:569-655
Timestamp: 2024-11-21T23:05:56.087Z
Learning: In the TypeScript file `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, the if-statement checking `filing.isExcluded` is temporary until the Candata API fix is implemented. This is noted in the TODO comment above it. Do not flag this as an issue in future code reviews.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:247-314
Timestamp: 2024-11-01T11:53:56.057Z
Learning: The team prefers to keep the existing implementation of the railway shipment processing logic in the `_fetchAndUpdateRailwayShipments` method in `shipment-tracking.service.ts` without further refactoring.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#174
File: apps/portal-api/src/shipment/shipment.service.ts:517-523
Timestamp: 2024-11-27T15:29:18.862Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, within the `submitShipmentEntry` method, if `relatedCanadaTariffs` is empty, it is intentional that all invoice lines are considered to have invalid HS codes.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#97
File: apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx:81-84
Timestamp: 2024-11-12T22:35:26.847Z
Learning: In the file `apps/frontend/src/modules/Shipment/views/ShipmentDetail/ShipmentDetail.page.tsx`, commented-out code may be intentionally kept for future use. Do not suggest removing such commented-out code in this file unless specifically instructed.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/core-agent.service.ts:248-321
Timestamp: 2025-04-16T19:48:08.355Z
Learning: The CoreAgentService.answerShipmentQuestions method is intentionally kept as a single method without further abstraction to maintain directness and clarity, even though it orchestrates multiple operations (shipment fetch, compliance fetch, answer generation).
```

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/process-document.handler.ts (22)</summary>

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#19
File: apps/portal-api/src/document/queues/document.processor.ts:72-74
Timestamp: 2024-10-22T19:06:49.145Z
Learning: In the 'DocumentProcessor' class in `apps/portal-api/src/document/queues/document.processor.ts`, errors are expected to be discarded, so rethrowing or wrapping the original error is not necessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:69-72
Timestamp: 2024-10-15T21:30:29.940Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, error handling in `spawnSplitJob` will be added when the `document-split-job` is implemented.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#138
File: apps/portal-api/src/shipment/shipment.service.ts:527-529
Timestamp: 2024-11-21T23:03:19.066Z
Learning: In `apps/portal-api/src/shipment/shipment.service.ts`, handling for potential null `importer` in the `submitShipmentEntry` method is added in later commits.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#586
File: apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts:0-0
Timestamp: 2025-05-27T19:53:04.368Z
Learning: In the Claro codebase, email sending functionality for accounting not completed warnings is moved from `apps/portal-api/src/shipment/listeners/shipment-accounting.listener.ts` to `accounting-not-completed-warning-email.sender.ts` in later commits as part of a refactoring to extract email logic to dedicated sender services.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:65-100
Timestamp: 2024-11-06T15:04:03.652Z
Learning: In the `ShipmentLinker` class (`apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts`), the commented out `partnerType` lines are intentional and should not be removed.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#37
File: apps/portal-api/src/document/queues/file.processor.ts:156-162
Timestamp: 2024-10-25T14:10:39.695Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, worker methods like `updateFileStatus` do not need to propagate the result and can return `void` instead of `boolean` since the worker doesn't need to know the result.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#37
File: apps/portal-api/src/document/queues/file.processor.ts:165-166
Timestamp: 2024-10-25T14:10:02.259Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, within the `spawnDataExtractionJob` method, failing to update the file status with `updateFileStatus` does not require throwing an error, as it only results in the status being displayed differently.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:247-314
Timestamp: 2024-11-01T11:53:56.057Z
Learning: The team prefers to keep the existing implementation of the railway shipment processing logic in the `_fetchAndUpdateRailwayShipments` method in `shipment-tracking.service.ts` without further refactoring.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#55
File: apps/portal-api/src/document/services/document.service.ts:108-141
Timestamp: 2024-10-30T18:14:17.736Z
Learning: In the `updateDocumentFields` method in `apps/portal-api/src/document/services/document.service.ts`, using `documentRepository.save(document)` ensures that all edits, deletions, and creations on `document.fields` are performed within a single transaction by TypeORM, so an explicit transaction is unnecessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#12
File: apps/portal-api/src/document/queues/file.processor.ts:21-21
Timestamp: 2024-10-16T17:16:10.592Z
Learning: In `apps/portal-api/src/document/queues/file.processor.ts`, the use of `forwardRef` is due to the scope, and there is no circular dependency.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#579
File: apps/portal-api/src/email/processors/generate-email-response.processor.ts:205-218
Timestamp: 2025-05-26T13:54:51.665Z
Learning: In the Claro codebase, EmailIntentType enum constants use UPPER_SNAKE_CASE names for code readability (e.g., GET_SHIPMENT_STATUS), but their actual string values are stored in kebab-case format (e.g., "get-shipment-status") to match the database storage format. The intent storage logic in process-user-intents.processor.ts converts intents to kebab-case using toLowerCase().replaceAll(/_/g, "-"), ensuring keys match between storage and retrieval operations.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#81
File: apps/portal-api/src/document/services/aggregation/linkers/shipment.linker.ts:199-199
Timestamp: 2024-11-06T19:18:12.548Z
Learning: In the `ShipmentLinker` class, the `tradePartnerPromise()` and `locationPromise()` functions each only modify their own arrays (`pendingTradePartners` and `pendingLocations`), so they can be safely executed concurrently using `Promise.all()`.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:338-368
Timestamp: 2024-11-01T11:56:32.221Z
Learning: The team prefers to keep the existing error handling in the `carrierShipmentTracking` method in `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts` without consolidating it into a reusable method.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#70
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:205-216
Timestamp: 2024-11-05T22:41:32.491Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, it's acceptable to perform shipment updates and tracking history creation without wrapping them in a transaction.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.controller.ts:66-74
Timestamp: 2024-11-14T22:34:15.458Z
Learning: In our NestJS codebase, error handling—such as checking for non-existent shipments—is handled within service methods like `getShipmentDutySummary` in `ShipmentService.ts`. Therefore, adding explicit error handling in the corresponding controller methods in `ShipmentController.ts` is unnecessary.
```

```
Learnt from: danny-aistribute
PR: AIstribute/Claro#14
File: apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx:25-25
Timestamp: 2024-10-16T21:26:11.352Z
Learning: In `apps/frontend/src/modules/Shipment/views/ShipmentList/ShipmentList.page.tsx`, error handling is managed in the service layer, so additional error handling in the component is unnecessary.
```

```
Learnt from: hieu-antek
PR: AIstribute/Claro#63
File: apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts:208-226
Timestamp: 2024-11-01T11:34:22.122Z
Learning: In `apps/backoffice-api/src/shipment-tracking/shipment-tracking.service.ts`, when handling async Axios calls in TypeScript, it's acceptable to handle errors by chaining `.catch()` methods instead of using `try...catch` blocks.
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#507
File: apps/portal-api/src/core-agent/services/core-agent.service.ts:248-321
Timestamp: 2025-04-16T19:48:08.355Z
Learning: The CoreAgentService.answerShipmentQuestions method is intentionally kept as a single method without further abstraction to maintain directness and clarity, even though it orchestrates multiple operations (shipment fetch, compliance fetch, answer generation).
```

```
Learnt from: SylvesterCC
PR: AIstribute/Claro#538
File: apps/portal-api/src/core-agent/context-builders/shipment-identifiers.context.ts:9-18
Timestamp: 2025-05-05T10:36:07.755Z
Learning: Context builder functions like `buildShipmentIdentifiersContext` always have a shipment present when called, as this is a design invariant of the system. Defensive null checks for the shipment parameter are unnecessary in these functions.
```

```
Learnt from: alex-aistribute
PR: AIstribute/Claro#110
File: apps/portal-api/src/shipment/shipment.service.ts:339-340
Timestamp: 2024-11-14T22:41:39.440Z
Learning: In the TypeScript codebase, error handling for the Candata service integration is implemented within the `getCandataShipment()` function in `CandataService` (`src/candata/candata.service.ts`), so additional error handling in `ShipmentService` methods like `getShipmentDutySummary` is unnecessary.
```

```
Learnt from: sophie-aistribute
PR: AIstribute/Claro#48
File: apps/frontend/src/modules/Shipment/views/CreateShipment/CreateShipment.page.tsx:62-64
Timestamp: 2024-10-28T19:28:44.730Z
Learning: Do not suggest adding loading messages to indicate processing states in the `ShipmentForm` component within `CreateShipment.page.tsx`.
```

</details>

</details><details>
<summary>🧬 Code Graph Analysis (5)</summary>

<details>
<summary>apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts (4)</summary><blockquote>

<details>
<summary>apps/portal-api/src/core-agent/testing/test-fragment-deduplication-fix.js (2)</summary>

* `context` (275-275)
* `fragments` (154-154)

</details>
<details>
<summary>apps/portal-api/src/core-agent/testing/test-rush-processing-quick.js (2)</summary>

* `context` (233-246)
* `fragments` (166-166)

</details>
<details>
<summary>apps/portal-api/src/core-agent/testing/test-processor-direct.js (2)</summary>

* `context` (379-379)
* `fragments` (251-251)

</details>
<details>
<summary>apps/portal-api/src/core-agent/constants/question-categories.constants.ts (1)</summary>

* `QUESTION_CATEGORIES` (1-60)

</details>

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts (2)</summary><blockquote>

<details>
<summary>apps/portal-api/src/aggregation/executer/database.logger.ts (1)</summary>

* `error` (44-50)

</details>
<details>
<summary>apps/portal-api/src/core-agent/types/response-fragment.types.ts (1)</summary>

* `ValidatedIntent` (114-114)

</details>

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-hold-shipment.handler.ts (3)</summary><blockquote>

<details>
<summary>apps/portal-api/src/core-agent/testing/test-processor-direct.js (1)</summary>

* `instructions` (401-403)

</details>
<details>
<summary>apps/portal-api/src/core-agent/types/response-fragment.types.ts (1)</summary>

* `ValidatedIntent` (114-114)

</details>
<details>
<summary>apps/portal-api/src/agent-context/services/shipment-context.service.ts (1)</summary>

* `ShipmentContextWithServices` (31-42)

</details>

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/update-shipment.handler.ts (5)</summary><blockquote>

<details>
<summary>apps/portal-api/src/core-agent/testing/test-processor-direct.js (4)</summary>

* `instructions` (401-403)
* `validatedIntent` (234-238)
* `context` (379-379)
* `fragments` (251-251)

</details>
<details>
<summary>apps/portal-api/src/core-agent/testing/test-fragment-deduplication-fix.js (3)</summary>

* `validatedIntent` (134-138)
* `context` (275-275)
* `fragments` (154-154)

</details>
<details>
<summary>apps/portal-api/src/core-agent/testing/test-rush-processing-quick.js (4)</summary>

* `validatedIntent` (159-163)
* `validatedIntent` (227-231)
* `context` (233-246)
* `fragments` (166-166)

</details>
<details>
<summary>apps/portal-api/src/core-agent/testing/test-request-rush-processing-integration.js (2)</summary>

* `validatedIntent` (283-287)
* `fragments` (303-303)

</details>
<details>
<summary>apps/portal-api/src/core-agent/types/response-fragment.types.ts (2)</summary>

* `ValidatedIntent` (114-114)
* `ResponseFragment` (70-79)

</details>

</blockquote></details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/base-intent-handler.ts (2)</summary><blockquote>

<details>
<summary>apps/portal-api/src/core-agent/types/shipment-context.types.ts (1)</summary>

* `ShipmentContext` (13-175)

</details>
<details>
<summary>apps/portal-api/src/core-agent/types/response-fragment.types.ts (2)</summary>

* `ConsolidatedFragmentContext` (85-108)
* `ResponseFragment` (70-79)

</details>

</blockquote></details>

</details><details>
<summary>🪛 Biome (1.9.4)</summary>

<details>
<summary>apps/portal-api/src/core-agent/handlers/process-document.handler.ts</summary>

[error] 394-394: Change to an optional chain.

Unsafe fix: Change to an optional chain.


(lint/complexity/useOptionalChain)

</details>

</details>

</details>

<details>
<summary>⏰ Context from checks skipped due to timeout of 90000ms. You can increase the timeout in your CodeRabbit configuration to a maximum of 15 minutes (900000ms). (1)</summary>

* GitHub Check: rush-incremental-build

</details>

<details>
<summary>🔇 Additional comments (44)</summary><blockquote>

<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/status-line.njk (1)</summary>

`1-20`: **LGTM! Well-structured template with good documentation.**

The template follows good practices with clear data dependencies documentation, appropriate conditional rendering, and proper fallback logic for the customs status. The HTML structure is suitable for email content.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/acknowledge-missing-documents-messages.njk (1)</summary>

`1-21`: **LGTM! Well-structured message template with clear user communication.**

The template provides a logical flow of information with appropriate conditional rendering and clear messaging to guide users about missing documents. The data dependencies are well-documented.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/shipment-identifiers.njk (1)</summary>

`13-18`: **LGTM! Proper handling of container display logic.**

The template correctly handles the distinction between multiple containers and single container display with appropriate whitespace control.

</details>
<details>
<summary>apps/portal-api/test-classification.js (1)</summary>

`4-6`: **LGTM! Proper NestJS application context setup for testing.**

The script correctly creates a NestJS application context for testing the email intent classification service. Based on project learnings, test scripts like this are often temporary utilities.

</details>
<details>
<summary>apps/portal-api/src/core-agent/services/shipment-response.service.ts (1)</summary>

`87-123`: **Excellent implementation of consolidated message merging!**

The enhanced `deduplicateFragments` method properly handles the new consolidated template system by merging `mainMessages` arrays for the "consolidated/main-messages" template while maintaining existing behavior for other templates. The implementation includes proper null/undefined checks and helpful debug logging for monitoring the merge process.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/main-messages.njk (1)</summary>

`18-23`: **✅ Included templates verified – no missing files**

All referenced templates in `apps/portal-api/src/core-agent/templates/consolidated/messages/` are present, so the conditional includes in `main-messages.njk` will not fail at runtime. No further action required.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/hold-shipment-messages.njk (1)</summary>

`1-20`: **Well-structured template with clear conditional logic!**

The template properly handles hold shipment acknowledgment messages with conditional rendering based on acknowledgment existence and backoffice alert status. The documentation clearly outlines data dependencies and the implementation follows the established consolidated template patterns.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/shipment-update-messages.njk (1)</summary>

`1-28`: **Comprehensive template for shipment update messages!**

The template effectively handles shipment update success messages with conditional rendering for acknowledgment, updated fields list with values, and shipment ID display. The implementation is well-documented and follows the consolidated template patterns consistently.

</details>
<details>
<summary>apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts (1)</summary>

`1139-1147`: **Excellent consolidation of document-related intents!**

The updated intent mapping logically groups document creation and update operations under "PROCESS_DOCUMENT" while giving UPDATE_SHIPMENT its own specific mapping. This consolidation aligns perfectly with the broader consolidated fragment system and should improve downstream processing consistency.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/acknowledge-documents-messages.njk (1)</summary>

`1-24`: **Well-structured template with clear conditional logic.**

The template implementation looks good with proper Nunjucks syntax, clear data dependency documentation, and logical conditional rendering based on shipment status and document availability. The HTML structure is clean and the template effectively handles different scenarios for document acknowledgment.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/rush-messages.njk (1)</summary>

`41-54`: **Submission result handling looks comprehensive.**

The conditional logic for handling submission results and errors provides good coverage of different scenarios. The structure appropriately handles success, failure, and error cases.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/cad-messages.njk (1)</summary>

`11-49`: **Good structure with consistent transaction number handling.**

The template effectively handles different customs statuses with appropriate messaging. The consistent conditional display of transaction numbers across different status branches is well-implemented, and the fallback case provides good coverage for unknown statuses.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/rns-messages.njk (1)</summary>

`10-52`: **Well-structured conditional logic for different RNS scenarios.**

The template effectively handles different customs statuses and RNS data availability scenarios. The conditional logic is clear and provides appropriate messaging for each state.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/manual-processing-messages.njk (1)</summary>

`10-20`: **Clean and straightforward implementation.**

The template has a simple, effective structure with appropriate conditional logic for acknowledgment messages and backoffice alert handling. The implementation is clean and easy to understand.

</details>
<details>
<summary>apps/portal-api/src/core-agent/testing/e2e-email-pipeline-nestjs.js (1)</summary>

`19-20`: **Well-implemented attachment flag behavior change.**

The change from including attachments by default to requiring an explicit `--with-attachments` flag is consistently implemented throughout the script, with proper documentation updates and clear console messages.



Also applies to: 33-37, 205-205, 229-230, 389-391

</details>
<details>
<summary>apps/portal-api/src/core-agent/types/response-fragment.types.ts (1)</summary>

`81-109`: **Well-structured interface for the consolidated template system.**

The `ConsolidatedFragmentContext` interface is properly typed with string literal unions for validation fields, optional properties for flexibility, and clear structure that supports the new consolidated fragment generation approach.

</details>
<details>
<summary>apps/portal-api/src/core-agent/templates/consolidated/messages/document-processing-messages.njk (1)</summary>

`17-30`: **No changes needed: Nunjucks autoescaping and safe defaults cover XSS and null handling**  

The Core-Agent module enables Nunjucks autoescape by default, so all output (including `document.filename`, `document.contentType`, `document.status`, and `document.claroUrl`) is HTML-escaped automatically. Nunjucks’s default `throwOnUndefined: false` means missing or undefined properties render as empty strings rather than throwing errors. Upstream code guarantees `message.attachments.documents` contains valid objects, so explicit `| e` filters or null-wrapping in the template aren’t required.

• libraries/nest-modules/src/types/template-manager.types.ts – DEFAULT_NUNJUCKS_CONFIG sets `autoescape: true`  
• apps/portal-api/src/core-agent/core-agent.module.ts – inherits `autoescape: true` in its nunjucksConfig  
• Nunjucks default `throwOnUndefined` is false, safely handling undefined values  

Please ignore the previous suggestions to add manual escaping and null checks here. 

> Likely an incorrect or invalid review comment.

</details>
<details>
<summary>apps/portal-api/src/core-agent/services/email-intent-analysis.service.ts (1)</summary>

`26-29`: **Excellent refactoring to support dynamic intent classification.**

The integration with `IntentHandlerRegistry` for dynamic intent metadata and the consolidation of document-related intents to `PROCESS_DOCUMENT` are well-implemented. The `getFallbackIntents` method provides clear descriptions and examples for intents without handlers.



Also applies to: 31-93, 133-135, 246-252

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/acknowledge-missing-documents.handler.ts (1)</summary>

`22-51`: **Clean refactoring to the consolidated fragment system.**

The handler has been successfully migrated to use the new consolidated fragment generation approach with:
- Proper error handling and comprehensive logging
- Clear separation of concerns with dedicated methods for building messages and options
- Maintains the original functionality while improving maintainability



Also applies to: 57-72, 78-99

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts (4)</summary>

`57-86`: **LGTM! Clean refactor to consolidated template system.**

The handle method properly implements the new 3-step pattern (build messages, determine options, create fragments) with appropriate logging at each stage.

---

`121-201`: **Well-structured message building with proper prioritization.**

The method effectively consolidates multiple message types with clear priority ordering. The duplicate prevention logic for general status messages (lines 183-199) is well thought out.

---

`13-30`: **Documentation improvements look good!**

The migration comment clearly explains the refactoring context, and the expanded description better captures the handler's comprehensive functionality.

---

`57-86`: **Well-structured refactoring of the handle method!**

The three-step approach (build messages → determine options → create fragments) provides clear separation of concerns. The improved logging with consistent prefixes will help with debugging and monitoring.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/acknowledge-documents.handler.ts (2)</summary>

`104-119`: **No null-check needed for smartTemplateContext**

The `ShipmentContext` interface defines `smartTemplateContext` as a required, non-optional property, and it’s always populated by the context builder (using `safeEvaluator.evaluate` with an empty default). You can safely access `context.smartTemplateContext.transportMode` without additional null checks.

---

`25-51`: **Excellent error handling and consistent refactoring pattern!**

The try-catch block provides comprehensive error handling with detailed logging. The three-step pattern maintains consistency across handlers.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-rns-proof.handler.ts (2)</summary>

`76-113`: **Excellent error handling in RNS proof generation.**

The method properly handles RNS generation failures and allows the template to handle error states gracefully. Good defensive programming.

---

`86-102`: **Excellent error handling pattern for RNS proof generation!**

The try-catch block allows graceful degradation when RNS generation fails, letting the template handle the error state. This provides a better user experience than throwing an error.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-cad-document.handler.ts (3)</summary>

`106-115`: **Keep the `cadMessage` null check**

The guard is required because `buildCadMessage` can return `null` (e.g. when `shipment.customsStatus === "pending-arrival"`), so omitting the `if (cadMessage)` check would cause a runtime error or unintended behavior.

No changes needed here. 

> Likely an incorrect or invalid review comment.

---

`29-30`: **Good clarification of the CAD acronym!**

Explicitly stating "Customs Accounting Document" improves code readability for developers unfamiliar with customs terminology.

---

`183-186`: **Good practice cleaning base64 data!**

Removing whitespace characters from base64 strings prevents potential issues with systems that expect unformatted base64 data.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-rush-processing.handler.ts (2)</summary>

`34-78`: **Well-implemented submission logic with proper error handling.**

The `attemptSubmission` method properly validates preconditions, handles errors gracefully, and provides meaningful return values for template rendering.

---

`132-139`: **sendBackofficeAlert handles its own errors—no additional error handling needed.**

The `sendBackofficeAlert` implementation in `BaseIntentHandler` is fully wrapped in a try/catch. It logs failures and returns an empty `backofficeAlerts` object on error, never letting exceptions bubble up. Calls to this method do not require further error handling.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/process-document.handler.ts (2)</summary>

`49-85`: **LGTM! Clean refactoring to consolidated fragment system.**

The migration to the consolidated fragment system is well-structured with clear steps:
1. Building main messages
2. Determining conditional fragment flags  
3. Using the consolidated fragment system

The error handling and logging are appropriate.

---

`444-463`: **Well-structured helper method for building document processing messages.**

Good separation of concerns by extracting message building logic into a dedicated method. The template-based approach with attachments is clean and maintainable.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/documentation-coming.handler.ts (1)</summary>

`25-54`: **Clean implementation of consolidated fragment system.**

The refactoring follows the established pattern consistently. Good error handling and logging throughout.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-manual-processing.handler.ts (1)</summary>

`31-69`: **Well-implemented consolidated fragment system with proper validation.**

Good job validating that instructions are provided before processing. The error handling and logging are comprehensive.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/base-intent-handler.ts (1)</summary>

`211-256`: **Excellent implementation of the consolidated fragment system.**

The `createConsolidatedFragments` method provides a clean, standardized way to generate response fragments. The conditional logic for showing validation issues and document status is well thought out.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/request-hold-shipment.handler.ts (1)</summary>

`31-72`: **Consistent implementation of the consolidated fragment pattern.**

The refactoring maintains consistency with other handlers while properly validating input and handling errors. Good use of the base class methods.

</details>
<details>
<summary>apps/portal-api/src/core-agent/handlers/update-shipment.handler.ts (6)</summary>

`46-48`: **LGTM!**

Good use of the standardized error fragment creation for early returns.

---

`53-55`: **LGTM!**

Clear error message that guides the user on what information is needed.

---

`59-74`: **Well-structured refactoring following the consolidated fragment pattern**

The three-step process is clearly implemented with appropriate logging at each stage.

---

`185-206`: **LGTM!**

Appropriate configuration for the update shipment handler which doesn't require additional validation displays.

---

`211-244`: **Well-implemented message builders**

Both methods provide consistent structure and appropriate context for the consolidated templates.

---

`249-259`: **No duplication found – `createErrorFragments` is not in BaseIntentHandler**

I checked `BaseIntentHandler` and there is no `createErrorFragments` method defined there. Your private implementation is necessary:

- Base class provides helpers like `createHighPriorityFragment`, but no array-based `createErrorFragments`.
- You may optionally replace  
  ```ts
  private createErrorFragments(reason: string): ResponseFragment[] {
    return [{
      template: "system-unavailable",
      priority: 1,
      fragmentContext: { reason }
    }];
  }
  ```
  with  
  ```ts
  private createErrorFragments(reason: string): ResponseFragment[] {
    return [this.createHighPriorityFragment("system-unavailable", { reason })];
  }
  ```
  for consistency, but the existing implementation is valid.

Proceed without merging this into the base class. 

> Likely an incorrect or invalid review comment.

</details>

</blockquote></details>

</details>

<!-- This is an auto-generated comment by CodeRabbit for review status -->
--
