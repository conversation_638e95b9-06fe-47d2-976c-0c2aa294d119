# Template Consolidation PRD: Reducing Fragment Duplication

## Executive Summary

The current ResponseFragment system is architecturally sound but has **template proliferation** issues. We have 40+ templates with overlapping concerns and inconsistent structures. This PRD outlines consolidating templates into **6 core fragments** while preserving all existing code infrastructure.

## Current System Analysis

### What Works Well (Keep As-Is)
- **ResponseFragment interface** - Clean, flexible structure
- **ShipmentResponseService** - Excellent rendering pipeline (deduplication, priority sorting, context merging)
- **Intent handler pattern** - BaseIntentHandler with helper methods
- **Fragment priority system** - Effective ordering mechanism
- **Context merging** - Seamless main + fragment context combination
- **Template engine integration** - TemplateManagerService works perfectly

### The Problem: Template Proliferation
- **40+ templates** with overlapping logic
- **Inconsistent structures** - Same data displayed differently
- **Duplication** - Similar conditional logic across multiple templates
- **Maintenance burden** - Changes require updates across multiple files
- **Testing complexity** - Many template combinations to validate

## Data Analysis: Response Structure Patterns

After analyzing all response examples, **every response follows this exact 6-fragment structure**:

```
1. [Main Message(s)] - Intent-specific content
2. [Validation Issues] - Missing docs/fields (conditional)
3. [Details Header] - Literal "Details:" text
4. [Shipment Identifiers] - CCN#, Container#, HBL#
5. [Document Status] - HBL/AN/EMF/CI&PL status (conditional)
6. [Status Line] - Final status + signature
```

### Conditional Fragment Rules

**Validation Issues** - Shows when:
- Missing documents (CI & PL for pending-commercial-invoice)
- Missing fields (weight, port code, CCN, OGD filing status)

**Document Status** - Shows when:
- Status is `pending-commercial-invoice` (shows HBL, AN/EMF)
- Status is `pending-confirmation` (shows HBL, AN/EMF, CI & PL)

**Additional Validation** - Shows when:
- There are compliance issues beyond missing documents (CCN missing, Weight missing, OGD filing Pending)

## Solution: Template Consolidation

### Consolidated Fragment Structure

Replace 40+ templates with **6 core templates**:

```typescript
// These replace all existing templates
const CONSOLIDATED_TEMPLATES = [
  'consolidated/main-messages',      // Priority 1
  'consolidated/validation-issues',  // Priority 2  
  'consolidated/details-header',     // Priority 3
  'consolidated/shipment-identifiers', // Priority 4
  'consolidated/document-status',    // Priority 5
  'consolidated/status-line'         // Priority 6
];
```

### Data Structure Enhancement

Extend the existing fragment context pattern:

```typescript
interface ConsolidatedFragmentContext extends ShipmentContext {
  // NEW: Cumulative main messages from multiple intents
  mainMessages?: Array<{
    content: string;
    priority: number;
    attachments?: { cadDocument?: any; rnsData?: any };
  }>;
  
  // NEW: Validation issues structure
  validationIssues?: {
    missingDocuments?: ('CI_PL' | 'HBL' | 'AN_EMF')[];
    missingFields?: ('weight' | 'port_code' | 'ccn' | 'ogd_filing')[];
  };
  
  // NEW: Explicit control over conditional fragments
  showValidationIssues?: boolean;
  showDocumentStatus?: boolean;
  showAdditionalValidation?: boolean;
  
  // All existing ShipmentContext properties are inherited
}
```

## Implementation Plan

### Phase 1: Create Consolidated Templates

#### 1.1 Main Messages Template
**File**: `apps/portal-api/src/core-agent/templates/consolidated/main-messages.njk`

```njk
{# Handles multiple intents with priority ordering #}
{% if mainMessages %}
  {% for message in mainMessages | sort(attribute='priority') %}
    <p>{{ message.content }}</p>
  {% endfor %}
{% endif %}
```

#### 1.2 Validation Issues Template  
**File**: `apps/portal-api/src/core-agent/templates/consolidated/validation-issues.njk`

```njk
{# Shows missing documents and fields when present #}
{% if showValidationIssues %}
  {# Missing documents section #}
  {% if validationIssues.missingDocuments and validationIssues.missingDocuments.length > 0 %}
    {% for doc in validationIssues.missingDocuments %}
      {% if doc == 'CI_PL' %}CI & PL: <strong>Missing</strong><br/>{% endif %}
    {% endfor %}
  {% endif %}
  
  {# Missing fields section - single line with spaces between #}
  {% if validationIssues.missingFields and validationIssues.missingFields.length > 0 %}
    {% for field in validationIssues.missingFields %}
      {%- if field == 'weight' %}Weight: <strong>missing</strong> {% endif -%}
      {%- if field == 'port_code' %}Port code <strong>missing</strong> {% endif -%}
      {%- if field == 'ccn' %}CCN <strong>missing</strong> {% endif -%}
      {%- if field == 'ogd_filing' %}OGD filing <strong>Pending</strong> {% endif -%}
    {% endfor %}<br/>
  {% endif %}
{% endif %}
```

#### 1.3 Details Header Template
**File**: `apps/portal-api/src/core-agent/templates/consolidated/details-header.njk`

```njk
<p><strong><u>Details:</u></strong>
```

#### 1.4 Shipment Identifiers Template
**File**: `apps/portal-api/src/core-agent/templates/consolidated/shipment-identifiers.njk`

```njk
{% if shipmentIdentifiers.cargoControlNumber %}CCN#: <strong>{{ shipmentIdentifiers.cargoControlNumber }}</strong>{% if shipmentIdentifiers.hasMultipleContainers or shipmentIdentifiers.primaryContainer %}, {% endif %}{% endif %}
{%- if shipmentIdentifiers.hasMultipleContainers -%}
Containers#: <strong>{{ shipmentIdentifiers.formattedContainers }}</strong>
{%- elif shipmentIdentifiers.primaryContainer -%}
Container#: <strong>{{ shipmentIdentifiers.primaryContainer }}</strong>
{%- endif -%}
{%- if shipmentIdentifiers.hblNumber %}, HBL#: <strong>{{ shipmentIdentifiers.hblNumber }}</strong>{% endif %}<br/>
```

#### 1.5 Document Status Template
**File**: `apps/portal-api/src/core-agent/templates/consolidated/document-status.njk`

```njk
{# Shows document receipt status for specific statuses #}
{% if showDocumentStatus and documentReceiptStatus %}
  HBL: <strong>{% if documentReceiptStatus.hblReceived %}Received{% else %}Missing{% endif %}</strong> 
  AN/EMF: <strong>{% if documentReceiptStatus.anEmfReceived %}Received{% else %}Missing{% endif %}</strong>
  {% if shipment.customsStatus == 'pending-confirmation' %} CI & PL: <strong>{% if documentReceiptStatus.ciReceived %}Received{% else %}Missing{% endif %}</strong>{% endif %}<br/>
{% endif %}
```

#### 1.6 Status Line Template
**File**: `apps/portal-api/src/core-agent/templates/consolidated/status-line.njk`

```njk
{# Additional validation line (when present) #}
{% if showAdditionalValidation and additionalValidation and additionalValidation.issues %}
  {% for issue in additionalValidation.issues %}{{ issue }} {% endfor %}<br/>
{% endif %}

Status: <strong>{{ formattedCustomsStatus or shipment.customsStatus }}</strong></p>

<p style="margin-top: 20px;">
Best regards,<br/>
Claro Customs
</p>
```

### Phase 2: Update Intent Handlers

#### 2.1 Handler Pattern Changes

**MINIMAL CHANGES** to existing handlers. Example for `GetShipmentStatusHandler`:

```typescript
// BEFORE (current)
async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
  const fragments: ResponseFragment[] = [];
  
  // Add answer fragments
  if (etaQuestion) {
    fragments.push({
      template: "answer-eta-template",
      priority: 1,
      fragmentContext: { answer: etaAnswer }
    });
  }
  
  // Add status fragment
  fragments.push({
    template: "core-agent/fragments/status-message",
    priority: 10,
    fragmentContext: complianceDetails
  });
  
  // Add details fragment
  fragments.push({
    template: "core-agent/fragments/details",
    priority: 11
  });
  
  return fragments;
}

// AFTER (consolidated)
async handle(validatedIntent: ValidatedIntent, context: ShipmentContext): Promise<ResponseFragment[]> {
  const fragments: ResponseFragment[] = [];
  const mainMessages: Array<{content: string, priority: number}> = [];
  
  // Build main messages
  if (etaQuestion) {
    mainMessages.push({
      content: `The ETA for your shipment is ${etaAnswer}.`,
      priority: 1
    });
  }
  
  mainMessages.push({
    content: this.buildStatusMessage(context),
    priority: 10
  });
  
  // Use the helper method with validation issues
  return this.createConsolidatedFragments(mainMessages, context, {
    validationIssues: this.buildValidationIssues(context)
  });
}
```

#### 2.2 Helper Method Addition

Add to `BaseIntentHandler`:

```typescript
/**
 * Create standardized fragments using consolidated templates
 */
protected createConsolidatedFragments(
  mainMessages: Array<{content: string, priority: number}>,
  context: ShipmentContext,
  options: {
    showValidationIssues?: boolean;
    showDocumentStatus?: boolean;
    showAdditionalValidation?: boolean;
    validationIssues?: ConsolidatedFragmentContext['validationIssues'];
    additionalValidation?: { issues: string[] };
  } = {}
): ResponseFragment[] {
  // Build validation issues if not provided
  const validationIssues = options.validationIssues ?? this.buildValidationIssues(context);
  
  const consolidatedContext = {
    ...context,
    mainMessages,
    validationIssues,
    additionalValidation: options.additionalValidation,
    showValidationIssues: options.showValidationIssues ?? 
      (!!validationIssues && 
       ((validationIssues.missingDocuments?.length > 0) || 
        (validationIssues.missingFields?.length > 0))),
    showDocumentStatus: options.showDocumentStatus ?? 
      ['pending-commercial-invoice', 'pending-confirmation'].includes(context.shipment.customsStatus),
    showAdditionalValidation: options.showAdditionalValidation ?? 
      (!!options.additionalValidation?.issues && options.additionalValidation.issues.length > 0)
  };
  
  return [
    { template: 'consolidated/main-messages', priority: 1, fragmentContext: consolidatedContext },
    { template: 'consolidated/validation-issues', priority: 2, fragmentContext: consolidatedContext },
    { template: 'consolidated/details-header', priority: 3, fragmentContext: consolidatedContext },
    { template: 'consolidated/shipment-identifiers', priority: 4, fragmentContext: consolidatedContext },
    { template: 'consolidated/document-status', priority: 5, fragmentContext: consolidatedContext },
    { template: 'consolidated/status-line', priority: 6, fragmentContext: consolidatedContext }
  ];
}
```

### Phase 3: Message Building Logic

#### 3.1 Status Message Builders

Create reusable message builders:

```typescript
// In BaseIntentHandler or utility service

/**
 * Build validation issues from shipment context
 */
protected buildValidationIssues(context: ShipmentContext): ConsolidatedFragmentContext['validationIssues'] | null {
  const issues: ConsolidatedFragmentContext['validationIssues'] = {
    missingDocuments: [],
    missingFields: []
  };
  
  // Check for missing documents
  if (context.shipment.customsStatus === 'pending-commercial-invoice') {
    if (!context.documentReceiptStatus?.ciReceived) {
      issues.missingDocuments.push('CI_PL');
    }
  }
  
  // Check for missing fields from compliance details
  if (context.complianceDetails?.hasMissingFields) {
    // Parse missing fields from complianceDetails.formattedMissingFields
    if (context.missingFieldsAnalysis?.weight === false) {
      issues.missingFields.push('weight');
    }
    if (context.missingFieldsAnalysis?.portCode === false) {
      issues.missingFields.push('port_code');
    }
    if (!context.shipment.cargoControlNumber) {
      issues.missingFields.push('ccn');
    }
    if (context.complianceDetails?.ogdFilingStatus === 'pending') {
      issues.missingFields.push('ogd_filing');
    }
  }
  
  // Return null if no issues
  if (issues.missingDocuments.length === 0 && issues.missingFields.length === 0) {
    return null;
  }
  
  return issues;
}

/**
 * Build status message based on intent and customs status
 */
protected buildStatusMessage(context: ShipmentContext): string {
  const { shipment, smartTemplateContext } = context;
  
  switch (shipment.customsStatus) {
    case 'pending-commercial-invoice':
      return 'Please send the missing document shown below at your earliest convenience for the subject shipment, so we can file customs without delay.';
    
    case 'pending-confirmation':
      return 'There are compliance issues or missing required fields preventing submission of the subject shipment. Please respond to the email and provide additional information for the missing fields.';
    
    case 'pending-arrival':
      return smartTemplateContext.hasETA 
        ? `The estimated time of arrival (ETA) at the port for the subject shipment is ${smartTemplateContext.etaDate}. We expect to submit the customs entry as the arrival date approaches.`
        : 'The submission is pending the shipment\'s arrival. We expect to submit the customs entry as the arrival date approaches.';
    
    case 'live':
      return 'The submission for the subject shipment has been initiated. We will let you know once released by customs.';
    
    case 'entry-submitted':
      return 'The entry for the subject shipment has been submitted. We will let you know once released by customs.';
    
    case 'entry-accepted':
      return 'The subject shipment entry has been accepted by Customs and is awaiting arrival of goods.';
    
    case 'exam':
      return 'The subject shipment has been selected by customs for examination. We are contacting you for further information.';
    
    case 'released':
      return smartTemplateContext.formattedReleaseDate 
        ? `The subject shipment has been released by CBSA on ${smartTemplateContext.formattedReleaseDate}.`
        : 'The subject shipment has been released by CBSA.';
    
    default:
      return `Status update: ${shipment.customsStatus}`;
  }
}

protected buildCadMessage(context: ShipmentContext): string {
  const { shipment } = context;
  
  switch (shipment.customsStatus) {
    case 'pending-commercial-invoice':
      return 'We can\'t provide the CAD yet as we\'re missing the following document(s). Please send them at your earliest convenience so we can proceed with filing.';
    
    case 'pending-confirmation':
      return 'We\'re currently unable to provide the CAD as there are compliance issues or missing required fields. Please respond to the email and provide additional information for the missing fields.';
    
    case 'pending-arrival':
      // Handler should trigger backoffice alert instead of showing message
      return null; // No user-facing message for this case
    
    default:
      return 'Please see CAD document attached.';
  }
}

protected buildRushMessage(context: ShipmentContext): string {
  const { shipment, smartTemplateContext } = context;
  
  switch (shipment.customsStatus) {
    case 'pending-commercial-invoice':
    case 'pending-confirmation':
      // For these statuses, rush uses the same message as general_status
      return this.buildStatusMessage(context);
      
    case 'pending-arrival':
    case 'live':
      return 'We\'ve received your rush request and will be submitting the entry right away.';
    
    case 'entry-submitted':
      return 'The entry for the subject shipment has already been submitted. We will let you know once released by customs.';
    
    case 'entry-accepted':
      return 'The subject shipment entry has already been submitted and accepted by Customs and is awaiting arrival of goods.';
    
    case 'exam':
      return 'Please note the shipment has been selected for exam and will be released once the examination is complete.';
    
    case 'released':
      return smartTemplateContext.formattedReleaseDate 
        ? `The subject shipment has been released by CBSA on ${smartTemplateContext.formattedReleaseDate}.`
        : 'The subject shipment has been released by CBSA.';
        
    default:
      return `Status update: ${shipment.customsStatus}`;
  }
}
```

## QA Issues Addressed

This design specifically resolves the following issues identified by QA:

| Issue # | Problem | Solution |
|---------|---------|----------|
| **4** | Missing status updates | Status line fragment always included |
| **6** | Missing fields on single line | Validation issues template with proper formatting |
| **7** | Missing validation errors/status | Always show validation issues when present |
| **8** | Incorrect document status | Accurate document status fragment based on actual data |
| **9** | Update requests not working | (Backend issue - not template related) |
| **10** | Rush requests not triggering submission | Handler logic ensures proper message and side effects |
| **11** | Missing CAD/transaction numbers | Can be included in main messages with proper data |

## Testing Strategy

### Unit Tests
- **Template rendering** - Test each consolidated template with various data combinations
- **Message builders** - Test status message generation for all statuses
- **Fragment context** - Test conditional fragment display logic

### Integration Tests
- **Multi-intent handling** - Test multiple intents producing cumulative main messages
- **Priority ordering** - Test main messages appear in correct order
- **Context merging** - Test fragment context combines properly with main context

### Regression Tests
- **Existing functionality** - Ensure all current responses still generate correctly
- **QA issue resolution** - Verify identified QA issues are addressed

## Migration Strategy

### Phase 1: Side-by-Side Implementation
1. Create consolidated templates alongside existing ones
2. Implement helper methods in BaseIntentHandler
3. Update one handler at a time to use consolidated approach
4. Compare outputs to ensure consistency

### Phase 2: Handler Migration
1. Start with simple handlers (GetShipmentStatusHandler)
2. Progress to complex handlers (ProcessDocumentHandler)
3. Test thoroughly at each step

### Phase 3: Cleanup
1. Remove old templates once all handlers migrated
2. Update template order constants
3. Clean up unused imports and methods

## Benefits

### For Developers
- **Consistent structure** - Every response follows the same pattern
- **Reduced complexity** - Only 6 templates to understand
- **Easier testing** - Predictable output structure
- **Maintainability** - Changes happen in one place

### For Users
- **Consistent experience** - All responses have the same format
- **Complete information** - No missing status lines or validation issues
- **Better readability** - Standardized formatting

### For QA
- **Addresses identified issues** - Consistent status lines, validation display, document status
- **Reduced test matrix** - Fewer template combinations to validate
- **Predictable behavior** - Responses always follow same structure

## Implementation Notes

### Example: Multiple Intent Handling

When processing multiple intents (e.g., GET_SHIPMENT_STATUS + REQUEST_CAD_DOCUMENT):

```typescript
const mainMessages = [];

// From GET_SHIPMENT_STATUS handler
mainMessages.push({
  content: "Your shipment was released by CBSA on March 15th.",
  priority: 1
});

// From REQUEST_CAD_DOCUMENT handler  
mainMessages.push({
  content: "Please see CAD document attached.",
  priority: 2
});

// Both messages appear in the response, ordered by priority
```

### Preserving Existing Code
- **ShipmentResponseService** - No changes needed
- **Fragment interface** - No changes needed  
- **Context merging** - No changes needed
- **Priority system** - No changes needed
- **Template engine** - No changes needed

### Key Changes
- **Template consolidation** - 40+ templates → 6 templates
- **Handler patterns** - Use helper methods for consistent fragment creation
- **Message building** - Centralized status message generation
- **Context structure** - Added mainMessages array and conditional flags

This approach maintains all the architectural benefits of the current system while eliminating template proliferation and ensuring consistent response structures.

## Appendix: Complete Message Mappings

### Intent: ACKNOWLEDGE_DOCUMENTS (All docs received)
- **Ocean/Air**: "We have received the required documents for your shipment and are currently reviewing and processing them."
- **Truck**: "We have received the required documents for your shipment and are currently reviewing and processing them. Transaction# will be sent to you shortly."

### Intent: ACKNOWLEDGE_MISSING_DOCUMENTS
- Same as general_status for pending-commercial-invoice

### Intent: DOCUMENTATION_COMING
- **Default**: Same as general_status for current status
- **Follow-up**: "Thank you for the update. We'll be waiting for the additional or updated documents."

### Intent: SEND_RNS
- **pending-commercial-invoice/pending-confirmation**: "We can't provide the RNS yet..." (with validation issues)
- **pending-arrival/live**: "The RNS can only be provided after the shipment has been submitted to customs."
- **entry-submitted**: "The RNS can only be provided after the shipment has been accepted by customs."
- **entry-accepted/exam**: "RNS information:" (with RNS data)
- **released**: "RNS Proof of Release information:" (with RNS data)

### Special Handling Notes

1. **Transaction Numbers**: When available, append to main message or include in shipment identifiers
2. **ETA Dates**: Include in main message for exam status and pending-arrival when available
3. **Release Dates**: Include in released status message when available
4. **CAD Attachments**: Handler must set attachment data in fragment context
5. **Backoffice Alerts**: Handler triggers alerts separately; not shown in user-facing messages
6. **Additional Validation Line**: Shows "CCN missing Weight missing OGD filing Pending" when applicable
7. **Fragment Deduplication**: ShipmentResponseService automatically deduplicates by template name