# Implementation Guide: Template Consolidation with Claude Code

## Context Engineering Strategy

Based on 2025 best practices for AI coding agents, this guide outlines the optimal approach for implementing the Template Consolidation PRD using Claude Code.

## Key Principles

1. **Context is King**: The success of implementation depends on context quality, not prompt complexity
2. **Treat <PERSON> as a Collaborative Engineer**: Work with it as you would a smart but less experienced colleague
3. **Provide Complete Contextual Coverage**: Include all relevant information upfront
4. **Use Examples and Patterns**: Show existing code patterns for <PERSON> to follow

## Recommended Approach: Multi-Phase with Persistent Context

### Phase 1: Context Setup and Validation (Single Prompt)

```markdown
I need to implement a template consolidation project. Here's the complete context:

1. **PRD**: [Attach template_consolidation_prd.md]
2. **Current System Overview**: 
   - We have a ResponseFragment system that works well
   - The issue is template proliferation (40+ templates)
   - We're consolidating to 6 core templates
   - All existing infrastructure stays the same

3. **Key Files to Review First**:
   - /apps/portal-api/src/core-agent/processors/handle-request-message.processor.ts
   - /apps/portal-api/src/core-agent/services/shipment-response.service.ts
   - /apps/portal-api/src/core-agent/handlers/base-intent.handler.ts
   - /apps/portal-api/src/core-agent/types/response-fragment.types.ts

4. **Example Handler to Understand Pattern**:
   - /apps/portal-api/src/core-agent/handlers/get-shipment-status.handler.ts

Please:
1. Review the PRD and confirm your understanding
2. Examine the current system architecture
3. Identify any potential issues or clarifications needed
4. Create a step-by-step implementation plan

DO NOT start implementation yet - just understand and plan.
```

### Phase 2: Template Creation (Separate Prompt, Same Context)

```markdown
Now let's create the 6 consolidated templates. Based on your review:

1. Create the directory: /apps/portal-api/src/core-agent/templates/consolidated/
2. Implement all 6 templates as specified in the PRD
3. Ensure proper Nunjucks syntax and null safety
4. Show me each template after creation for validation

Reference the PRD section "Phase 1: Create Consolidated Templates" for exact specifications.
```

### Phase 3: Base Handler Updates (Separate Prompt, Same Context)

```markdown
Let's update the BaseIntentHandler with new helper methods:

1. Add the buildValidationIssues() method
2. Add the buildStatusMessage() method  
3. Add the buildCadMessage() method
4. Add the buildRushMessage() method
5. Add the createConsolidatedFragments() helper

Reference existing patterns in the current BaseIntentHandler for consistency.
Show me the updated file with clear comments on changes.
```

### Phase 4: Handler Migration (Multiple Prompts with Sub-Agents)

For each handler, use a focused prompt:

```markdown
Let's migrate GetShipmentStatusHandler to use consolidated templates:

Current State:
- Creates multiple fragments with different templates
- Handles question classification for specific answers

Target State:
- Use createConsolidatedFragments() helper
- Build mainMessages array with all content
- Set proper boolean flags for conditional fragments

Requirements:
1. Preserve all existing functionality
2. Maintain backward compatibility
3. Add comprehensive logging for debugging
4. Keep existing error handling

Show me:
1. The complete updated handler
2. A diff showing what changed
3. Any potential issues you see
```

## Sub-Agent Strategy

For complex handlers, use specialized sub-agents:

### Sub-Agent 1: Message Builder
```markdown
You are a Message Builder agent. Your task:
1. Analyze all intent/status combinations for [INTENT_NAME]
2. Create appropriate message content using buildStatusMessage() pattern
3. Handle all edge cases from the PRD appendix
4. Ensure messages match exactly what's in the data dump
```

### Sub-Agent 2: Validation Inspector
```markdown
You are a Validation Inspector agent. Your task:
1. Review the migrated handler code
2. Compare output with original handler
3. Verify all QA issues are addressed
4. Check for missing edge cases
```

### Sub-Agent 3: Test Generator
```markdown
You are a Test Generator agent. Your task:
1. Create comprehensive tests for the migrated handler
2. Test all status/intent combinations
3. Verify fragment output matches expected structure
4. Include edge cases from QA issues
```

## Context Management Best Practices

### 1. Use CLAUDE.md Effectively
Update CLAUDE.md with project-specific context:
```markdown
## Current Project: Template Consolidation

### Goal
Consolidating 40+ templates into 6 core templates while preserving ResponseFragment architecture.

### Key Patterns
- All handlers return 6 standardized fragments
- Use createConsolidatedFragments() helper
- Build mainMessages array for content
- Set boolean flags for conditional fragments

### Testing Commands
rushx jest src/core-agent/handlers --watch
node src/core-agent/testing/test-handler-output.js [handler-name]
```

### 2. Provide Examples
Always include before/after examples:
```markdown
Here's how the current handler works:
[code example]

Here's how it should work after migration:
[code example]
```

### 3. Incremental Validation
After each phase:
```markdown
Please validate:
1. Does this match the PRD requirements?
2. Will this integrate with existing code?
3. Are there any edge cases not covered?
4. Show me a sample output for status: [specific status]
```

## Common Pitfalls to Avoid

1. **Don't Dump Everything at Once**: Phase your prompts for better results
2. **Don't Skip Validation**: Always verify understanding before implementation
3. **Don't Ignore Existing Patterns**: Reference current code for consistency
4. **Don't Forget Edge Cases**: Explicitly mention QA issues and special cases

## Rollback Strategy

Include in your context:
```markdown
If we encounter issues:
1. All changes are isolated to new /consolidated/ directory
2. Handlers can be reverted individually
3. Original templates remain untouched until Phase 3
4. Each handler migration is a separate commit
```

## Success Metrics

Define clear success criteria:
```markdown
Success means:
1. All handlers produce identical output structure
2. All QA issues are resolved
3. Template count reduced from 40+ to 6
4. All tests pass
5. No regression in functionality
```

## Important Considerations

### Handler Registration
All handlers are registered in `CoreAgentModule` INTENT_HANDLERS provider. No changes needed there.

### Side Effects Management
Handlers may trigger side effects (backoffice alerts, document generation). These remain in handler logic, not in templates:
- CAD generation for pending-arrival → trigger backoffice alert
- Rush processing → trigger submission workflow
- Document processing → update document status

### Error Handling
The fragment system gracefully handles errors:
- Failed fragments are skipped
- Other fragments still render
- Empty responses trigger manual review

### Testing Approach
For each migrated handler:
1. Run existing tests to ensure no regression
2. Compare output with original handler using test scripts
3. Verify all QA issues are addressed
4. Test with multiple intents to ensure proper message stacking

## Final Tip: The Power of Context

Remember: "The secret to building truly effective AI agents has less to do with the complexity of the code you write, and everything to do with the quality of the context you provide."

Provide Claude Code with:
- Clear objectives
- Complete context
- Existing patterns to follow
- Specific examples
- Validation criteria

This will yield far better results than complex prompts or trying to be too clever with instructions.