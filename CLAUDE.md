# CLAUDE.md

This file provides guidance to <PERSON> (claude.ai/code) when working with code in this repository.

## Claude Code Efficiency Guidelines

**Parallel Sub-Agent Usage:**
- Use parallel sub-agents (Task tool) for independent information gathering tasks
- Consolidate and verify results from multiple sub-agents before proceeding
- Ideal for: codebase exploration, pattern analysis, multi-file searches, parallel testing
- Always check sub-agent outputs for consistency and completeness
- Use single consolidated response after parallel work is complete

## Project Overview

This is Claro, a customs automation platform built as a Rush monorepo. The system processes trade documents, manages customs filings, and provides automated compliance checking for import/export operations.

**Main Applications:**
- `portal` - Customer-facing React frontend
- `portal-api` - Main NestJS backend API with LLM integration
- `backoffice` - Admin React frontend  
- `backoffice-api` - Admin NestJS backend
- `bullmq-board` - Queue monitoring dashboard
- `cloud-functions` - Firebase cloud functions

**Shared Libraries:**
- `libraries/nest-modules` - Shared NestJS modules, entities, DTOs, and services
- `libraries/ui` - Shared React UI components
- `tools/utils` - Shared utilities

## Common Commands

**Development Setup:**
```bash
# Install packages (use Rush, never npm install directly)
rush update

# Build all projects
rush build

# Start development servers
rush dev                    # All services in parallel
rush fe                     # Frontend portal only  
rush start -t portal        # Specific project
rush start -t portal-api    # API server with watch mode

# Start supporting services (Redis, Postgres, etc.)
rush services
```

**Testing:**
```bash
# NEVER run Jest tests - use core-agent testing scripts instead
# cd apps/portal-api && rushx jest  # ❌ DON'T USE

# Use core-agent testing scripts (preferred)
cd apps/portal-api && ./src/core-agent/testing/run-e2e-with-logs.sh

# Check TypeScript in frontend apps
cd apps/portal && rushx lint

# Use parallel sub-agents for testing multiple components or scenarios
# Consolidate test results before reporting status
```

**Database Operations:**
```bash
# Generate migration (from portal-api directory)
cd apps/portal-api && npx typeorm migration:generate src/migrations/MigrationName -d src/data-source.ts

# Run migrations
cd apps/portal-api && npx typeorm migration:run -d src/data-source.ts

# Database queries (NEVER use direct SQL/psql)
cd apps/portal-api && node src/core-agent/testing/db-query.js orgs
cd apps/portal-api && node src/core-agent/testing/db-query.js sql "SELECT * FROM shipment LIMIT 5"
```

## Architecture Overview

**Backend (NestJS):**
- Event-driven architecture with `EventEmitter2`
- BullMQ for background job processing
- TypeORM with PostgreSQL database
- Multi-tenant with organization scoping via `ClsService`
- LLM integration for document processing and AI features

**Frontend (React + TypeScript):**
- Vite build system with React + SWC
- MobX for state management
- TanStack Query for server state
- Tailwind CSS for styling
- React Router for navigation

**Key Backend Patterns:**
- Use `@Scope(Scope.REQUEST)` and `ClsService` for multi-tenancy
- Inject `EventEmitter2` for event publishing
- Use `@OnEvent()` decorators for event listeners
- Background jobs with `@Processor()` classes extending `WorkerHost`
- LLM calls through `AskLLMService` with typed schemas

**Core-Agent Architecture (LLM Integration):**
- **Layer 1**: Intent classification from user emails/requests
- **Layer 2**: Intent handlers process validated intents and return response fragments
- **Layer 3**: Template rendering with Nunjucks templates
- **ShipmentContextService**: Single source of truth for all business rule evaluation
- **Response fragments**: Prioritized template pieces that compose final responses
- **Side effects**: Handlers can trigger backoffice alerts, document generation, etc.

**Database:**
- All entities extend `BaseEntity` with common fields (id, createdAt, updatedAt, organizationId)
- Use scoping helpers like `inOrgCls()` for tenant isolation
- TypeORM repositories with `getFindOptions()` helper for standardized querying

## Key Domain Models

**Core Entities:**
- `Organization` - Multi-tenant root entity
- `User` - User accounts with role-based access
- `TradePartner` - Import/export business partners
- `Shipment` - Cargo shipments being processed
- `Document` - Trade documents (invoices, bills of lading, etc.)
- `DocumentAggregation` - Grouped documents for customs filing
- `Email` - Processed email communications
- `Product` - Trade goods with HS codes and compliance data

**Compliance & Filing:**
- `OgdFiling` - Canadian customs filings
- `SimaFiling` - Anti-dumping/countervailing duty filings
- `MatchingRule` - Business rules for automated processing
- `UsTariff`/`CanadaTariff` - Tariff code databases

## Development Guidelines

**Parallel Development Workflows:**
- Use parallel sub-agents for independent file analysis tasks
- Consolidate findings before implementing changes
- Examples: analyzing multiple components, checking patterns across modules, investigating related files
- Always verify sub-agent outputs for accuracy and completeness
- Present unified findings after parallel research is complete

**Code Standards:**
- Follow functional programming patterns with pure functions
- Use TypeScript with explicit typing
- Implement JSDoc for functions over 10 lines
- Prefer composition over inheritance
- Use method chaining with Array methods (map, filter, reduce)

**NestJS Patterns:**
- Use path aliases `@/*` for imports
- Group imports: NestJS modules, project modules, external libraries
- Include Swagger decorators (`@ApiOperation`, `@ApiOkResponse`)
- Use dependency injection for services, repositories, queues
- Handle async operations with async/await
- Throw standard NestJS exceptions (`NotFoundException`, `BadRequestException`)

**Intent Handler Development:**
- Extend `BaseIntentHandler` for all intent handlers
- Use `ShipmentContextService` for business rule evaluation (never duplicate logic)
- Return `ResponseFragment[]` with template names and priorities
- Use helper methods: `createHighPriorityFragment()`, `safeExecute()`, `sendBackofficeAlert()`
- Register handlers in `CoreAgentModule` INTENT_HANDLERS provider
- Follow EMAIL_INTENTS mapping for consistent intent classification

**Database Patterns:**
- Use TypeORM repositories and QueryRunner for transactions
- Apply organization scoping with `inOrgCls()` helper
- Use `getFindOptions()` for standardized queries
- Follow migration naming: `TIMESTAMP-DescriptiveName.ts`

**Template Development:**
- Use Nunjucks templates in `apps/portal-api/src/core-agent/templates/`
- Organize by function: `status/`, `requests/`, `compliance/`, `shared/`
- Use kebab-case naming with state suffixes: `-success`, `-blocked`, `-not-ready`
- Always check for null/undefined values before using context properties
- Use priority ranges: 1-10 (critical), 11-20 (status), 21-30 (supporting), 31+ (errors/fallbacks)
- Specific answer templates for GET_SHIPMENT_STATUS: `answer-eta-template`, `answer-transaction-number-template`, etc.
- Use parallel sub-agents to analyze multiple template directories or patterns simultaneously
- Consolidate template analysis before making changes

**Frontend Patterns:**
- Use MobX stores for client state
- TanStack Query for server state with error boundaries
- Form validation with Formik + Yup
- Consistent component structure in modules (Types, Schema, Routes)

**Rush Monorepo Rules:**
- NEVER use `npm install` - always use `rush add -p package-name`
- Use `rush update` after adding dependencies
- Use `rushx command` to run package scripts from project directories
- Use `rush start -t project-name` to start specific projects

## Advanced Development Patterns

**ShipmentContextService Pattern:**
- Singleton scope with ModuleRef resolution for REQUEST-scoped dependencies
- Single source of truth for all business rule evaluation
- Use `safeEvaluate()` wrapper for error-resilient rule evaluation
- Pre-compute all business rules once per request, cache in context object
- Support QueryRunner for transaction consistency

**Response Fragment Architecture:**
- Responses built from prioritized template fragments
- Priority ranges: 1-10 (critical), 11-20 (status), 21-30 (supporting), 31+ (errors)
- Fragment context can override template variables
- Templates use null-safe patterns with fallback content

**LLM Integration Patterns:**
- Question classification for GET_SHIPMENT_STATUS with specific answer templates
- Fallback handling for missing data (transaction numbers, ETAs, etc.)
- Performance benchmarks: aim for <1000ms response times
- Mixed questions processed as single queries, not multiple classifications

## Testing

**Core-Agent Testing (Preferred Approach):**
- Use specialized testing scripts in `apps/portal-api/src/core-agent/testing/`
- Always navigate to `apps/portal-api` directory first
- Use demo organization (ID 3) for safe testing
- Prefer logging scripts over direct node execution
- Use parallel sub-agents for testing multiple intent handlers or scenarios simultaneously
- Consolidate test results and verify consistency before reporting
- See comprehensive testing guide: `/home/<USER>/dev/Claro/.cursor/rules/core-agent-testing.mdc`

**CRITICAL TESTING RULES:**
- **NEVER run Jest tests** - Use core-agent testing scripts instead
- **NEVER use direct SQL/psql** - Always use `db-query.js` for database access
- **ALWAYS use .sh scripts** - Prefer `run-e2e-with-logs.sh` (calls `e2e-email-pipeline-nestjs.js`) and `run-processor-test-with-logs.sh` (calls `test-processor-direct.js`) for automatic logging

**Key Testing Scripts:**
```bash
# Intent handler testing (shell script calls test-processor-direct.js)
./src/core-agent/testing/run-processor-test-with-logs.sh --intent=GET_SHIPMENT_STATUS --verbose

# E2E email pipeline testing (shell script calls e2e-email-pipeline-nestjs.js)
./src/core-agent/testing/run-e2e-with-logs.sh

# Database queries (preferred over direct psql)
node src/core-agent/testing/db-query.js orgs
node src/core-agent/testing/db-query.js sql "SELECT * FROM shipment LIMIT 5"

# Find test shipments
node src/core-agent/testing/find-test-shipments.js --analysis
```

**Template Testing Requirements:**
- Always run `rushx build` after creating new templates
- Templates must be compiled to `dist/` directory before use
- Use `--no-side-effects` flag to prevent actual email sending during development

**Traditional Jest Testing:**
- **❌ DO NOT USE** - Jest tests are disabled for this project
- Use core-agent testing scripts instead
- Test files follow pattern `*.spec.ts` but should not be executed
- Mock external dependencies and use request-scoped testing where needed

## Core-Agent System Architecture

**Email Processing Pipeline:**
- Emails processed through intent classification → handler execution → template rendering
- 9 main intent types: GET_SHIPMENT_STATUS, REQUEST_CAD_DOCUMENT, PROCESS_DOCUMENT, etc.
- Use `ShipmentContextService` as single source of truth for business rules
- Context includes pre-computed flags: `canRush`, `isCompliant`, `canGenerateCAD`, etc.
- Side effects tracked in context: document generation, backoffice alerts, etc.

**Data Aggregation System:**
- Located in `apps/portal-api/src/aggregation/`
- Components: adapters, generators, intents, executers, transformers, workflows
- Processing flow: define intent → generate data → transform → execute workflow → process through adapters
- Includes unit conversion and field fallback mechanisms

## Important Notes

- This is a multi-tenant system - always consider organization scoping
- Email processing is a core feature with complex parsing and aggregation
- LLM integration is used extensively for document understanding and question classification
- Queue processing handles heavy background tasks
- Database migrations must be run in development and production environments
- The system processes sensitive trade and customs data - follow security best practices
- Always use demo organization (ID 3) for testing to avoid affecting production data